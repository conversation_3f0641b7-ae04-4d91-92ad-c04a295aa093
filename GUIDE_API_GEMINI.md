# Guide de Configuration de l'API Gemini

## État Actuel

L'application est maintenant configurée pour utiliser le **mode AI par défaut** avec affichage détaillé des erreurs.

## Configuration de l'API Gemini

### Option 1: Variable d'Environnement (Recommandée)

1. Créez un fichier `.env` dans le répertoire racine :
```
GEMINI_API_KEY=votre_vraie_cle_api_ici
```

2. La clé sera automatiquement chargée par `config.php`

### Option 2: Modification Directe du Code

Modifiez la ligne 12 dans `ai_studio_code.py` :
```python
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "VOTRE_VRAIE_CLE_API_ICI")
```

## Obtenir une Clé API Gemini

1. Allez sur [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Connectez-vous avec votre compte Google
3. Cliquez sur "Create API Key"
4. Copiez la clé générée

## Test de l'Application

### Mode AI (Par Défaut)
- Ouvrez `http://localhost/facturation-electronique/index.php`
- Le mode "IA Gemini (extraction réelle)" est maintenant sélectionné par défaut
- Si l'API ne fonctionne pas, vous verrez des messages d'erreur détaillés comme :
  - ❌ Modèle Gemini non trouvé. Vérifiez votre clé API et les permissions.
  - ❌ Accès refusé à l'API Gemini. Vérifiez votre clé API et les quotas.
  - ❌ Limite de taux dépassée. Attendez quelques minutes avant de réessayer.

### Test Spécialisé
- Ouvrez `http://localhost/facturation-electronique/test_ai_mode.html`
- Interface dédiée au test du mode AI avec diagnostics avancés

## Messages d'Erreur Améliorés

L'application affiche maintenant des messages d'erreur clairs et utiles :

### Erreurs API Communes
- **404 Not Found** : Modèle non trouvé ou clé API invalide
- **403 Forbidden** : Accès refusé, vérifiez les permissions
- **429 Too Many Requests** : Limite de taux dépassée
- **Connection Error** : Problème de connexion internet

### Solutions Automatiques
- Messages d'erreur en français avec émojis
- Suggestions de solutions pour chaque type d'erreur
- Possibilité de basculer vers le mode "mock" en cas de problème

## Fallback vers Mode Mock

Si l'API Gemini ne fonctionne pas, vous pouvez :
1. Changer le mode d'extraction vers "Données de test (rapide)"
2. L'application utilisera des données de test pour générer le XML
3. Cela permet de tester le reste de l'application sans dépendre de l'API

## Vérification du Fonctionnement

### Avec API Fonctionnelle
✅ Extraction réelle des données du PDF
✅ Génération XML basée sur les données extraites
✅ Téléchargement du fichier XML

### Avec API Non Fonctionnelle
❌ Message d'erreur détaillé affiché
💡 Suggestions de solutions
🔄 Possibilité de basculer vers le mode mock

## Fichiers Modifiés

1. **`index.php`** - Mode AI par défaut
2. **`ai_studio_code.py`** - Messages d'erreur améliorés
3. **`process.php`** - Gestion d'erreur détaillée
4. **`test_ai_mode.html`** - Interface de test spécialisée

## Prochaines Étapes

1. **Configurez votre vraie clé API Gemini**
2. **Testez avec `test_ai_mode.html`**
3. **Utilisez l'application principale avec le mode AI**
4. **En cas de problème, les erreurs détaillées vous guideront**
