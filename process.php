<?php
require_once 'config.php';

// Set error reporting to catch all errors
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors to avoid breaking JSON

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-control-allow-methods: POST');
header('Access-control-allow-headers: Content-Type');

// Function to ensure JSON response even on fatal errors
function sendJsonResponse($data) {
    // Clear any output that might have been generated
    if (ob_get_level()) {
        ob_clean();
    }

    echo json_encode($data);
    exit;
}

// Set up error handler for fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        sendJsonResponse([
            'success' => false,
            'error' => 'Erreur fatale du serveur: ' . $error['message']
        ]);
    }
});

// Configuration
define('UPLOAD_DIR', 'uploads/');
define('OUTPUT_DIR', 'output/');
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('PYTHON_SCRIPT', 'ai_studio_code.py');

// Create directories if they don't exist
if (!file_exists(UPLOAD_DIR)) {
    mkdir(UPLOAD_DIR, 0755, true);
}
if (!file_exists(OUTPUT_DIR)) {
    mkdir(OUTPUT_DIR, 0755, true);
}

try {
    // Log the start of the process
    logError("Process started");
    
    // Check if request method is POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Méthode non autorisée');
    }
    
    // Check if file was uploaded
    if (!isset($_FILES['pdf_file']) || $_FILES['pdf_file']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('Erreur lors du téléchargement du fichier');
    }
    
    $uploadedFile = $_FILES['pdf_file'];
    $extractionMode = $_POST['extraction_mode'] ?? 'mock';
    $outputFormat = $_POST['output_format'] ?? 'xml';

    // Get manual supplier data
    $supplierId = $_POST['supplier_id'] ?? '';
    $supplierName = $_POST['supplier_name'] ?? '';
    $supplierAddress = $_POST['supplier_address'] ?? '';
    $manualSupplierData = [
        'Supplier Fiscal ID' => $supplierId,
        'Supplier Name' => $supplierName,
        'Supplier Address' => $supplierAddress,
    ];
    
    // Validate file
    validateUploadedFile($uploadedFile);
    
    // Generate unique filename
    $timestamp = time();
    $uniqueId = uniqid();
    $originalName = pathinfo($uploadedFile['name'], PATHINFO_FILENAME);
    $uploadedFileName = $originalName . '_' . $timestamp . '_' . $uniqueId . '.pdf';
    $uploadedFilePath = UPLOAD_DIR . $uploadedFileName;
    
    // Move uploaded file
    if (!move_uploaded_file($uploadedFile['tmp_name'], $uploadedFilePath)) {
        throw new Exception('Impossible de sauvegarder le fichier');
    }
    
    // Process the file
    $result = processInvoice($uploadedFilePath, $extractionMode, $outputFormat, $manualSupplierData);
    
    // Clean up uploaded file
    unlink($uploadedFilePath);
    
    // Generate filename based on invoice number
    $invoiceNumber = $result['invoice_data']['Invoice Number'] ?? 'facture_' . time();
    $safeFilename = generateSafeFilename($invoiceNumber);

    // Return success response
    sendJsonResponse([
        'success' => true,
        'message' => 'Fichier traité avec succès',
        'xml_content' => $result['xml_content'],
        'invoice_data' => $result['invoice_data'],
        'processing_time' => $result['processing_time'],
        'extraction_mode' => $extractionMode,
        'filename' => $safeFilename . '.xml'
    ]);

} catch (Exception $e) {
    // Log the error for debugging
    logError("Exception caught: " . $e->getMessage());
    logError("Stack trace: " . $e->getTraceAsString());

    // Return error response
    sendJsonResponse([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function validateUploadedFile($file) {
    // Check file size
    if ($file['size'] > MAX_FILE_SIZE) {
        throw new Exception('Le fichier est trop volumineux (max 10MB)');
    }
    
    // Check file type
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    if ($mimeType !== 'application/pdf') {
        throw new Exception('Le fichier doit être un PDF');
    }
    
    // Check file extension
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if ($extension !== 'pdf') {
        throw new Exception('Extension de fichier non autorisée');
    }
}

function processInvoice($filePath, $extractionMode, $outputFormat, $manualSupplierData) {
    $startTime = microtime(true);
    
    // Create a temporary Python script that processes this specific file
    $tempPythonScript = createTempPythonScript($filePath, $extractionMode, $manualSupplierData);
    
    try {
        // Execute Python script
        $command = PYTHON_EXECUTABLE . " \"$tempPythonScript\" 2>&1";
        logError("Executing command: $command");
        $output = [];
        $returnCode = 0;
        
        // Log the command being executed
        logError("Executing command: $command");
        
        exec($command, $output, $returnCode);
        
        // Log the output and return code
        logError("Command output: " . implode("\n", $output));
        logError("Return code: $returnCode");
        
        if ($returnCode !== 0) {
            $errorOutput = implode("\n", $output);
            logError("Python script failed with return code $returnCode: $errorOutput");

            // Provide more user-friendly error messages
            if (strpos($errorOutput, 'n\'est pas reconnu') !== false || strpos($errorOutput, 'not recognized') !== false) {
                throw new Exception('❌ Python n\'est pas installé ou n\'est pas accessible. Veuillez vérifier l\'installation de Python.');
            } else if (strpos($errorOutput, 'No module named') !== false) {
                throw new Exception('❌ Modules Python manquants. Veuillez installer les dépendances requises (pip install requests).');
            } else if (strpos($errorOutput, '❌') !== false) {
                // Extract user-friendly error message from Python script
                $lines = explode("\n", $errorOutput);
                foreach ($lines as $line) {
                    if (strpos($line, '❌') !== false) {
                        throw new Exception($line);
                    }
                }
                throw new Exception('❌ Erreur lors du traitement: ' . $errorOutput);
            } else if (strpos($errorOutput, 'HTTP Error: 404') !== false) {
                throw new Exception('❌ Erreur API Gemini: Modèle non trouvé. Vérifiez votre clé API et les permissions.');
            } else if (strpos($errorOutput, 'HTTP Error: 403') !== false) {
                throw new Exception('❌ Erreur API Gemini: Accès refusé. Vérifiez votre clé API et les quotas.');
            } else if (strpos($errorOutput, 'HTTP Error: 429') !== false) {
                throw new Exception('❌ Erreur API Gemini: Limite de taux dépassée. Attendez quelques minutes avant de réessayer.');
            } else if (strpos($errorOutput, 'Connection Error') !== false) {
                throw new Exception('❌ Erreur de connexion: Impossible de se connecter à l\'API Gemini. Vérifiez votre connexion internet.');
            } else if (strpos($errorOutput, 'Timeout Error') !== false) {
                throw new Exception('❌ Timeout: L\'API Gemini met trop de temps à répondre. Réessayez plus tard.');
            } else {
                throw new Exception('❌ Erreur lors de l\'exécution du script Python: ' . $errorOutput);
            }
        }
        
        // Log that we're checking for the XML file
        logError("Checking for XML file: temp_output.xml");
        
        // Read the generated XML file
        $xmlFilePath = OUTPUT_DIR . 'temp_output.xml';
        if (!file_exists($xmlFilePath)) {
            logError("XML file not found: $xmlFilePath");
            throw new Exception('Fichier XML non généré');
        }
        
        $xmlContent = file_get_contents($xmlFilePath);
        if ($xmlContent === false) {
            throw new Exception('Impossible de lire le fichier XML généré');
        }
        
        // Try to read invoice data from JSON file if it exists
        $jsonFilePath = OUTPUT_DIR . 'temp_invoice_data.json';
        $invoiceData = null;
        if (file_exists($jsonFilePath)) {
            $jsonContent = file_get_contents($jsonFilePath);
            $invoiceData = json_decode($jsonContent, true);
        }
        
        // Clean up temporary files
        unlink($tempPythonScript);
        if (file_exists($xmlFilePath)) unlink($xmlFilePath);
        if (file_exists($jsonFilePath)) unlink($jsonFilePath);
        
        $processingTime = round((microtime(true) - $startTime) * 1000, 2);
        
        return [
            'xml_content' => $xmlContent,
            'invoice_data' => $invoiceData,
            'processing_time' => $processingTime
        ];
        
    } catch (Exception $e) {
        // Clean up on error
        if (file_exists($tempPythonScript)) unlink($tempPythonScript);
        if (file_exists(OUTPUT_DIR . 'temp_output.xml')) unlink(OUTPUT_DIR . 'temp_output.xml');
        if (file_exists(OUTPUT_DIR . 'temp_invoice_data.json')) unlink(OUTPUT_DIR . 'temp_invoice_data.json');
        throw $e;
    }
}

function createTempPythonScript($pdfFilePath, $extractionMode, $manualSupplierData) {
    $tempScriptPath = 'temp_process_' . uniqid() . '.py';
    
    $manualSupplierJson = json_encode($manualSupplierData);

    $pythonCode = '
import sys
import os
sys.path.append(".")

# Import our existing functions
from ai_studio_code import pdf_to_base64, extract_invoice_data_with_gemini, generate_teif_xml
import json

def main():
    pdf_path = "' . addslashes($pdfFilePath) . '"
    extraction_mode = "' . $extractionMode . '"
    manual_supplier_data = json.loads(\'' . addslashes($manualSupplierJson) . '\')
    
    try:
        # Convert PDF to base64
        base64_data = pdf_to_base64(pdf_path)
        if not base64_data:
            raise Exception("Failed to convert PDF to base64")
        
        # Extract invoice data
        if extraction_mode == "ai":
            invoice_data = extract_invoice_data_with_gemini(base64_data)
        else:
            # Use mock data
            invoice_data = {
                "Invoice Number": "FV/2025/0007",
                "Issue Date": "06/02/2025",
                "Supplier Fiscal ID": "1327372W",
                "Supplier Name": "SOLAR ENERGY SOLUTIONS",
                "Supplier Address": "Zone Industrielle, Sfax, Tunisia",
                "Client Fiscal ID": "08777647",
                "Client Name": "EMNA KACEM",
                "Client Address": "RTE SOKRA KM 5, Sfax Sud, Tunisia",
                "Line Items": [
                    {
                        "ItemCode": "**********",
                        "Description": "MODULE PHOTOVOLTAIQUE JA SOLAR MONO 565 Wc",
                        "Quantity": 6.0,
                        "UnitPrice": 540.0,
                        "TaxRate": 7.0
                    },
                    {
                        "ItemCode": "**********", 
                        "Description": "ONDULEUR SOLIS MONO 3 KW",
                        "Quantity": 1.0,
                        "UnitPrice": 1870.0,
                        "TaxRate": 7.0
                    }
                ],
                "Total HT Amount": 4332.0,
                "Total TTC Amount": 4542.88,
                "Stamp Duty": 1.0,
                "Amount in Words": "Quatre Mille Cinq Cent Quarante-Deux Dinar et Huit Cent Quatre-Vingts Millimes"
            }
        
        if not invoice_data:
            raise Exception("Failed to extract invoice data")
        
        # Generate TEIF XML
        xml_content = generate_teif_xml(invoice_data, manual_supplier_data)
        
        # Save XML to temporary file
        with open("output/temp_output.xml", "w", encoding="utf-8") as f:
            f.write(xml_content)
        
        # Save invoice data to JSON for PHP to read
        with open("output/temp_invoice_data.json", "w", encoding="utf-8") as f:
            json.dump(invoice_data, f, ensure_ascii=False, indent=2)
        
        print("SUCCESS: XML generated successfully")
        
    except Exception as e:
        print(f"ERROR: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
';
    
    // Log the attempt to create the temporary script
    logError("Creating temporary Python script: $tempScriptPath");
    
    // Try to create the temporary script
    $result = file_put_contents($tempScriptPath, $pythonCode);
    if ($result === false) {
        logError("Failed to create temporary Python script: $tempScriptPath");
        throw new Exception("Failed to create temporary Python script");
    }
    
    logError("Temporary Python script created successfully: $tempScriptPath");
    return $tempScriptPath;
}

// Helper function to generate safe filename from invoice number
function generateSafeFilename($invoiceNumber) {
    // Remove or replace invalid characters for filenames
    $safeFilename = $invoiceNumber;

    // Replace forward slashes with underscores
    $safeFilename = str_replace('/', '_', $safeFilename);

    // Replace other potentially problematic characters
    $safeFilename = str_replace(['\\', ':', '*', '?', '"', '<', '>', '|'], '_', $safeFilename);

    // Remove multiple consecutive underscores
    $safeFilename = preg_replace('/_+/', '_', $safeFilename);

    // Remove leading/trailing underscores
    $safeFilename = trim($safeFilename, '_');

    // Ensure filename is not empty
    if (empty($safeFilename)) {
        $safeFilename = 'facture_' . time();
    }

    // Limit filename length (Windows has 255 char limit, leave room for extension)
    if (strlen($safeFilename) > 200) {
        $safeFilename = substr($safeFilename, 0, 200);
    }

    return $safeFilename;
}

// Helper function to log errors (optional)
function logError($message) {
    $logFile = OUTPUT_DIR . 'error.log';
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}
?>
