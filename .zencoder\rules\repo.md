---
description: Repository Information Overview
alwaysApply: true
---

# Facturation Électronique Information

## Summary
This project is a Python-based tool for electronic invoicing (facturation électronique) in Tunisia. It processes PDF invoices, extracts data using AI (Gemini API), and generates TEIF (Tunisian Electronic Invoice Format) XML documents according to Tunisian tax authority specifications.

## Structure
- **ai_studio_code.py**: Main Python script for invoice processing and XML generation
- **Exemple_facture_qr.pdf**: Sample invoice with QR code for testing
- **FV_2025_0007.pdf**: Sample invoice file used in the code
- **exemple_signe_elfatoora.xml**: Example of a signed electronic invoice in XML format
- **facture_INVOIC_V1.8.8_withoutSig.xsd**: XML Schema for invoices without signatures
- **facture_INVOIC_V1.8.8_withSig.xsd**: XML Schema for invoices with signatures
- **Guide-Implementation-TEIF_V2.0.pdf**: Implementation guide for TEIF v2.0
- **Spec FTP v2.0.pdf**: FTP specifications for document transmission
- **SpecificationsTechniques DSS Signature Fournisseur.pdf**: Technical specifications for digital signatures
- **Spécifications web services v5.pdf**: Web services specifications v5

## Language & Runtime
**Language**: Python
**Version**: 3.x (specific version not specified in code)
**Dependencies**:
- requests: For API communication with Gemini
- xml.etree.ElementTree: For XML processing
- xml.dom.minidom: For XML formatting
- datetime: For date handling
- base64: For PDF encoding
- json: For parsing AI responses

## Functionality
**Main Components**:
- **PDF Processing**: Converts PDF invoices to base64 for AI processing
- **AI Integration**: Uses Gemini API to extract invoice data from PDFs
- **XML Generation**: Creates TEIF-compliant XML documents from extracted data

## Usage & Operations
```python
# Basic usage flow
pdf_path = "invoice.pdf"
base64_data = pdf_to_base64(pdf_path)
extracted_data = extract_invoice_data_with_gemini(base64_data)
teif_xml = generate_teif_xml(extracted_data)
```

## API Integration
**Gemini API**:
- **Endpoint**: https://generativelanguage.googleapis.com
- **Authentication**: API Key
- **Purpose**: Extract structured data from invoice PDFs

## Data Processing
**Invoice Data Extraction**:
- Invoice Number
- Issue Date
- Supplier/Client Information
- Line Items
- Tax Information
- Total Amounts

**XML Generation**:
- Creates TEIF v2.0 compliant XML
- Includes proper Tunisian tax authority elements
- Formats according to official specifications

## Technical Notes
- The code previously included digital signature functionality (using cryptography library) which has been removed
- Uses mock data for testing but can be configured to use actual AI extraction
- Outputs unsigned XML files that comply with TEIF standards