<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Final - Générateur TEIF XML</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1>Test Final - Générateur TEIF XML</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test avec Fichier PDF</h5>
                    </div>
                    <div class="card-body">
                        <form id="testForm" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="testFile" class="form-label">Sélectionner un fichier PDF:</label>
                                <input type="file" class="form-control" id="testFile" name="pdf_file" accept=".pdf" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="testExtractionMode" class="form-label">Mode d'extraction:</label>
                                <select class="form-select" id="testExtractionMode" name="extraction_mode">
                                    <option value="mock" selected>Mock (données de test)</option>
                                    <option value="ai">AI (nécessite API key)</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="testOutputFormat" class="form-label">Format de sortie:</label>
                                <select class="form-select" id="testOutputFormat" name="output_format">
                                    <option value="xml" selected>XML</option>
                                </select>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">Tester la Génération XML</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Résultats du Test</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <p class="text-muted">Aucun test effectué</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Détails de la Réponse</h5>
                    </div>
                    <div class="card-body">
                        <pre id="responseDetails" class="bg-light p-3" style="max-height: 400px; overflow-y: auto;">Aucune réponse</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultsDiv = document.getElementById('testResults');
            const detailsDiv = document.getElementById('responseDetails');
            
            resultsDiv.innerHTML = '<div class="test-result info">Test en cours...</div>';
            detailsDiv.textContent = 'Envoi de la requête...';
            
            console.log('Sending test request...');
            
            fetch('process.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Response received:', response);
                
                // Check if response is JSON
                const contentType = response.headers.get('content-type');
                console.log('Content-Type:', contentType);
                
                if (contentType && contentType.includes('application/json')) {
                    return response.json().then(data => ({
                        isJson: true,
                        data: data,
                        status: response.status,
                        statusText: response.statusText
                    }));
                } else {
                    return response.text().then(text => ({
                        isJson: false,
                        data: text,
                        status: response.status,
                        statusText: response.statusText
                    }));
                }
            })
            .then(result => {
                console.log('Parsed result:', result);
                
                let resultHtml = '';
                
                if (result.isJson) {
                    if (result.data.success) {
                        resultHtml = `
                            <div class="test-result success">
                                <strong>✅ Succès!</strong><br>
                                Fichier XML généré avec succès<br>
                                Temps de traitement: ${result.data.processing_time}ms<br>
                                Mode d'extraction: ${result.data.extraction_mode}<br>
                                Nom du fichier: ${result.data.filename}
                            </div>
                        `;
                        
                        if (result.data.xml_content) {
                            resultHtml += `
                                <div class="mt-3">
                                    <button class="btn btn-success" onclick="downloadXml('${result.data.filename}', \`${result.data.xml_content.replace(/`/g, '\\`')}\`)">
                                        Télécharger XML
                                    </button>
                                </div>
                            `;
                        }
                    } else {
                        resultHtml = `
                            <div class="test-result error">
                                <strong>❌ Erreur:</strong><br>
                                ${result.data.error}
                            </div>
                        `;
                    }
                } else {
                    resultHtml = `
                        <div class="test-result error">
                            <strong>❌ Réponse non-JSON:</strong><br>
                            Status: ${result.status} ${result.statusText}<br>
                            La réponse n'est pas au format JSON valide.
                        </div>
                    `;
                }
                
                resultsDiv.innerHTML = resultHtml;
                detailsDiv.textContent = JSON.stringify(result, null, 2);
            })
            .catch(error => {
                console.error('Error:', error);
                
                resultsDiv.innerHTML = `
                    <div class="test-result error">
                        <strong>❌ Erreur de connexion:</strong><br>
                        ${error.message}
                    </div>
                `;
                detailsDiv.textContent = `Error: ${error.message}\nStack: ${error.stack}`;
            });
        });
        
        function downloadXml(filename, content) {
            const blob = new Blob([content], { type: 'application/xml' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
