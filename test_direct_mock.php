<?php
require_once 'config.php';

echo "<h2>Test Direct du Mode Mock</h2>";

// Simuler une requête POST avec le mode mock
$_POST['extraction_mode'] = 'mock';
$_POST['output_format'] = 'xml';
$_POST['supplier_id'] = 'TEST123';
$_POST['supplier_name'] = 'Test Supplier';
$_POST['supplier_address'] = 'Test Address';

// Simuler un fichier uploadé
$testPdfPath = 'Exemple_facture_qr.pdf';

if (!file_exists($testPdfPath)) {
    echo "<div style='color: red;'>Fichier PDF de test non trouvé: $testPdfPath</div>";
    echo "<p>Créons un fichier PDF factice pour le test...</p>";
    
    // Créer un fichier PDF factice
    $fakePdfContent = "%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n179\n%%EOF";
    file_put_contents($testPdfPath, $fakePdfContent);
    echo "<div style='color: green;'>Fichier PDF factice créé</div>";
}

// Simuler $_FILES
$_FILES['pdf_file'] = [
    'name' => 'test_facture.pdf',
    'type' => 'application/pdf',
    'size' => filesize($testPdfPath),
    'tmp_name' => $testPdfPath,
    'error' => UPLOAD_ERR_OK
];

echo "<h3>Données de test:</h3>";
echo "Mode d'extraction: " . $_POST['extraction_mode'] . "<br>";
echo "Format de sortie: " . $_POST['output_format'] . "<br>";
echo "Fichier PDF: " . $testPdfPath . " (" . filesize($testPdfPath) . " bytes)<br>";

echo "<h3>Test du processus...</h3>";

try {
    // Inclure le code de process.php mais sans les headers
    ob_start();
    
    // Configuration
    define('UPLOAD_DIR', 'uploads/');
    define('OUTPUT_DIR', 'output/');
    define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
    define('PYTHON_SCRIPT', 'ai_studio_code.py');

    // Create directories if they don't exist
    if (!file_exists(UPLOAD_DIR)) {
        mkdir(UPLOAD_DIR, 0755, true);
    }
    if (!file_exists(OUTPUT_DIR)) {
        mkdir(OUTPUT_DIR, 0755, true);
    }

    // Log the start of the process
    logError("Test process started");
    
    // Check if file was uploaded
    if (!isset($_FILES['pdf_file']) || $_FILES['pdf_file']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('Erreur lors du téléchargement du fichier');
    }
    
    $uploadedFile = $_FILES['pdf_file'];
    $extractionMode = $_POST['extraction_mode'] ?? 'mock';
    $outputFormat = $_POST['output_format'] ?? 'xml';

    // Get manual supplier data
    $supplierId = $_POST['supplier_id'] ?? '';
    $supplierName = $_POST['supplier_name'] ?? '';
    $supplierAddress = $_POST['supplier_address'] ?? '';
    $manualSupplierData = [
        'Supplier Fiscal ID' => $supplierId,
        'Supplier Name' => $supplierName,
        'Supplier Address' => $supplierAddress,
    ];
    
    echo "Données du fournisseur: <pre>" . print_r($manualSupplierData, true) . "</pre>";
    
    // Generate unique filename
    $timestamp = time();
    $uniqueId = uniqid();
    $originalName = pathinfo($uploadedFile['name'], PATHINFO_FILENAME);
    $uploadedFileName = $originalName . '_' . $timestamp . '_' . $uniqueId . '.pdf';
    $uploadedFilePath = UPLOAD_DIR . $uploadedFileName;
    
    // Copy file (since we're using the original for testing)
    if (!copy($uploadedFile['tmp_name'], $uploadedFilePath)) {
        throw new Exception('Impossible de sauvegarder le fichier');
    }
    
    echo "<div style='color: green;'>✓ Fichier copié vers: $uploadedFilePath</div>";
    
    // Test Python execution directly
    echo "<h4>Test d'exécution Python...</h4>";
    
    $pythonPath = PYTHON_EXECUTABLE;
    $testCommand = "\"$pythonPath\" --version 2>&1";
    $output = [];
    $returnCode = 0;
    exec($testCommand, $output, $returnCode);
    
    echo "Commande Python: <code>$testCommand</code><br>";
    echo "Code de retour: $returnCode<br>";
    echo "Sortie: <pre>" . implode("\n", $output) . "</pre>";
    
    if ($returnCode === 0) {
        echo "<div style='color: green;'>✓ Python accessible</div>";
        
        // Create a simple mock test script
        $testScript = 'test_mock_simple_' . uniqid() . '.py';
        $testPythonCode = '
import sys
import json
import os

def main():
    print("Mock test script started")
    
    # Mock invoice data
    invoice_data = {
        "Invoice Number": "FV/2025/TEST",
        "Issue Date": "26/09/2025",
        "Supplier Fiscal ID": "TEST123",
        "Supplier Name": "Test Supplier",
        "Supplier Address": "Test Address",
        "Client Fiscal ID": "CLIENT123",
        "Client Name": "Test Client",
        "Client Address": "Client Address",
        "Line Items": [
            {
                "ItemCode": "TEST-001",
                "Description": "Test Item",
                "Quantity": 1.0,
                "UnitPrice": 100.0,
                "TaxRate": 19.0
            }
        ],
        "Total HT Amount": 100.0,
        "Total TTC Amount": 119.0,
        "Stamp Duty": 1.0,
        "Amount in Words": "Cent Dix-Neuf Dinars"
    }
    
    # Simple XML generation
    xml_content = """<?xml version="1.0" encoding="UTF-8"?>
<Invoice>
    <InvoiceNumber>{}</InvoiceNumber>
    <IssueDate>{}</IssueDate>
    <SupplierName>{}</SupplierName>
    <TotalAmount>{}</TotalAmount>
</Invoice>""".format(
        invoice_data["Invoice Number"],
        invoice_data["Issue Date"], 
        invoice_data["Supplier Name"],
        invoice_data["Total TTC Amount"]
    )
    
    # Save XML to temporary file
    with open("output/temp_output.xml", "w", encoding="utf-8") as f:
        f.write(xml_content)
    
    # Save invoice data to JSON for PHP to read
    with open("output/temp_invoice_data.json", "w", encoding="utf-8") as f:
        json.dump(invoice_data, f, ensure_ascii=False, indent=2)
    
    print("SUCCESS: Mock XML generated successfully")
    print("XML length:", len(xml_content))

if __name__ == "__main__":
    main()
';
        
        file_put_contents($testScript, $testPythonCode);
        
        $command = "\"$pythonPath\" \"$testScript\" 2>&1";
        echo "<h4>Exécution du script de test mock...</h4>";
        echo "Commande: <code>$command</code><br>";
        
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        echo "Code de retour: $returnCode<br>";
        echo "Sortie: <pre>" . implode("\n", $output) . "</pre>";
        
        // Check if files were created
        $xmlFile = OUTPUT_DIR . 'temp_output.xml';
        $jsonFile = OUTPUT_DIR . 'temp_invoice_data.json';
        
        if (file_exists($xmlFile)) {
            $xmlContent = file_get_contents($xmlFile);
            echo "<div style='color: green;'>✓ Fichier XML créé (" . strlen($xmlContent) . " caractères)</div>";
            echo "<h4>Contenu XML:</h4>";
            echo "<pre>" . htmlspecialchars($xmlContent) . "</pre>";
            
            if (file_exists($jsonFile)) {
                $jsonContent = file_get_contents($jsonFile);
                $invoiceData = json_decode($jsonContent, true);
                echo "<div style='color: green;'>✓ Fichier JSON créé</div>";
                
                echo "<h3>Résultat Final:</h3>";
                echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
                echo "<strong>✓ Test réussi!</strong><br>";
                echo "Le mode mock fonctionne correctement et génère du XML.";
                echo "</div>";
                
                // Clean up
                unlink($xmlFile);
                unlink($jsonFile);
            } else {
                echo "<div style='color: red;'>✗ Fichier JSON non créé</div>";
            }
        } else {
            echo "<div style='color: red;'>✗ Fichier XML non créé</div>";
        }
        
        // Clean up
        unlink($testScript);
        
    } else {
        echo "<div style='color: red;'>✗ Python non accessible</div>";
    }
    
    // Clean up uploaded file
    unlink($uploadedFilePath);
    
} catch (Exception $e) {
    echo "<div style='color: red;'>Exception: " . $e->getMessage() . "</div>";
    echo "<div style='color: red;'>Stack trace: <pre>" . $e->getTraceAsString() . "</pre></div>";
}

// Clean up fake PDF if we created it
if (file_exists($testPdfPath) && filesize($testPdfPath) < 1000) {
    unlink($testPdfPath);
}
?>
