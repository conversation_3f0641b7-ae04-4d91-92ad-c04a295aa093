<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Mode AI - Générateur TEIF XML</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-result { margin: 10px 0; padding: 15px; border-radius: 8px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .progress-container { display: none; }
        .progress-bar { transition: width 0.3s ease; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-robot"></i> Test Mode AI - Extraction Réelle
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Mode AI Activé:</strong> Ce test utilise l'API Gemini pour l'extraction réelle des données.
                            Les erreurs détaillées seront affichées si l'API ne fonctionne pas.
                        </div>
                        
                        <form id="aiTestForm" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="pdfFile" class="form-label">
                                    <i class="fas fa-file-pdf"></i> Fichier PDF à analyser:
                                </label>
                                <input type="file" class="form-control" id="pdfFile" name="pdf_file" accept=".pdf" required>
                                <div class="form-text">Sélectionnez une facture PDF pour test avec l'IA</div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Mode d'extraction:</label>
                                <select class="form-select" name="extraction_mode" id="extractionMode">
                                    <option value="ai" selected>🤖 IA Gemini (extraction réelle)</option>
                                    <option value="mock">🧪 Données de test (fallback)</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Format de sortie:</label>
                                <select class="form-select" name="output_format">
                                    <option value="xml" selected>XML TEIF</option>
                                </select>
                            </div>
                            
                            <div class="mb-3 border-top pt-3">
                                <h6>Informations Fournisseur (Optionnel - remplace les données extraites)</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" name="supplier_id" placeholder="Matricule Fiscal">
                                    </div>
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" name="supplier_name" placeholder="Nom Fournisseur">
                                    </div>
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" name="supplier_address" placeholder="Adresse">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-magic"></i> Extraire avec IA
                                </button>
                            </div>
                        </form>
                        
                        <!-- Progress Bar -->
                        <div class="progress-container mt-4" id="progressContainer">
                            <div class="d-flex justify-content-between mb-2">
                                <span id="progressText">Traitement en cours...</span>
                                <span id="progressPercent">0%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     id="progressBar" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>
                        
                        <!-- Results -->
                        <div id="results" class="mt-4"></div>
                        
                        <!-- Response Details -->
                        <div class="mt-4" id="detailsContainer" style="display: none;">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-code"></i> Détails Techniques
                                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="toggleDetails()">
                                            <i class="fas fa-eye"></i> Voir/Masquer
                                        </button>
                                    </h6>
                                </div>
                                <div class="card-body" id="detailsBody" style="display: none;">
                                    <pre id="responseDetails" class="bg-light p-3" style="max-height: 300px; overflow-y: auto; font-size: 12px;"></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let progressInterval;
        
        document.getElementById('aiTestForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultsDiv = document.getElementById('results');
            const detailsDiv = document.getElementById('responseDetails');
            const detailsContainer = document.getElementById('detailsContainer');
            
            // Show progress
            showProgress();
            resultsDiv.innerHTML = '';
            detailsContainer.style.display = 'none';
            
            console.log('🚀 Envoi de la requête AI...');
            
            fetch('process.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('📡 Réponse reçue:', response);
                
                // Check if response is JSON
                const contentType = response.headers.get('content-type');
                console.log('📋 Content-Type:', contentType);
                
                if (contentType && contentType.includes('application/json')) {
                    return response.json().then(data => ({
                        isJson: true,
                        data: data,
                        status: response.status,
                        statusText: response.statusText
                    }));
                } else {
                    return response.text().then(text => ({
                        isJson: false,
                        data: text,
                        status: response.status,
                        statusText: response.statusText
                    }));
                }
            })
            .then(result => {
                hideProgress();
                console.log('✅ Résultat traité:', result);
                
                let resultHtml = '';
                
                if (result.isJson) {
                    if (result.data.success) {
                        resultHtml = `
                            <div class="test-result success">
                                <h5><i class="fas fa-check-circle"></i> Extraction Réussie!</h5>
                                <p><strong>Mode:</strong> ${result.data.extraction_mode}</p>
                                <p><strong>Temps de traitement:</strong> ${result.data.processing_time}ms</p>
                                <p><strong>Fichier généré:</strong> ${result.data.filename}</p>
                                ${result.data.xml_content ? `
                                    <div class="mt-3">
                                        <button class="btn btn-success" onclick="downloadXml('${result.data.filename}', \`${result.data.xml_content.replace(/`/g, '\\`').replace(/\$/g, '\\$')}\`)">
                                            <i class="fas fa-download"></i> Télécharger XML
                                        </button>
                                    </div>
                                ` : ''}
                            </div>
                        `;
                    } else {
                        // Error case - show detailed error message
                        resultHtml = `
                            <div class="test-result error">
                                <h5><i class="fas fa-exclamation-triangle"></i> Erreur d'Extraction</h5>
                                <p><strong>Message:</strong></p>
                                <div class="alert alert-danger">
                                    ${result.data.error}
                                </div>
                                <div class="mt-3">
                                    <h6>Solutions possibles:</h6>
                                    <ul>
                                        <li>Vérifiez votre clé API Gemini dans le fichier <code>ai_studio_code.py</code></li>
                                        <li>Assurez-vous que votre clé API a les bonnes permissions</li>
                                        <li>Vérifiez votre connexion internet</li>
                                        <li>Essayez le mode "Données de test" comme alternative</li>
                                    </ul>
                                </div>
                            </div>
                        `;
                    }
                } else {
                    resultHtml = `
                        <div class="test-result error">
                            <h5><i class="fas fa-bug"></i> Erreur de Communication</h5>
                            <p><strong>Status:</strong> ${result.status} ${result.statusText}</p>
                            <p>La réponse du serveur n'est pas au format JSON valide.</p>
                            <div class="alert alert-warning">
                                Cela peut indiquer une erreur PHP ou un problème de configuration.
                            </div>
                        </div>
                    `;
                }
                
                resultsDiv.innerHTML = resultHtml;
                detailsDiv.textContent = JSON.stringify(result, null, 2);
                detailsContainer.style.display = 'block';
            })
            .catch(error => {
                hideProgress();
                console.error('❌ Erreur:', error);
                
                resultsDiv.innerHTML = `
                    <div class="test-result error">
                        <h5><i class="fas fa-wifi"></i> Erreur de Connexion</h5>
                        <p><strong>Message:</strong> ${error.message}</p>
                        <div class="alert alert-danger">
                            Impossible de communiquer avec le serveur. Vérifiez que le serveur web fonctionne.
                        </div>
                    </div>
                `;
                detailsDiv.textContent = `Error: ${error.message}\nStack: ${error.stack}`;
                document.getElementById('detailsContainer').style.display = 'block';
            });
        });
        
        function showProgress() {
            document.getElementById('progressContainer').style.display = 'block';
            let progress = 0;
            const messages = [
                'Lecture du fichier PDF...',
                'Conversion en Base64...',
                'Envoi à l\'API Gemini...',
                'Extraction des données...',
                'Génération du XML TEIF...',
                'Finalisation...'
            ];
            
            progressInterval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;
                
                const messageIndex = Math.floor((progress / 100) * messages.length);
                const message = messages[Math.min(messageIndex, messages.length - 1)];
                
                document.getElementById('progressBar').style.width = progress + '%';
                document.getElementById('progressPercent').textContent = Math.round(progress) + '%';
                document.getElementById('progressText').textContent = message;
            }, 800);
        }
        
        function hideProgress() {
            if (progressInterval) {
                clearInterval(progressInterval);
            }
            document.getElementById('progressBar').style.width = '100%';
            document.getElementById('progressPercent').textContent = '100%';
            document.getElementById('progressText').textContent = 'Terminé!';
            
            setTimeout(() => {
                document.getElementById('progressContainer').style.display = 'none';
            }, 1000);
        }
        
        function toggleDetails() {
            const detailsBody = document.getElementById('detailsBody');
            detailsBody.style.display = detailsBody.style.display === 'none' ? 'block' : 'none';
        }
        
        function downloadXml(filename, content) {
            const blob = new Blob([content], { type: 'application/xml' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
