# Génération de Noms de Fichiers TEIF

## 📋 Règle de Nommage

**Règle principale :** Le nom de fichier XML généré doit être basé sur la valeur de la balise `<DocumentIdentifier>` du XML TEIF, sans le caractère '/' et autres caractères interdits dans les noms de fichiers.

## 🔧 Implémentation

### Fonction de Génération (PHP)

```php
function generateSafeFilename($invoiceNumber) {
    // Remplace les slashes par des underscores
    $safeFilename = str_replace('/', '_', $invoiceNumber);
    
    // Remplace les autres caractères problématiques
    $safeFilename = str_replace(['\\', ':', '*', '?', '"', '<', '>', '|'], '_', $safeFilename);
    
    // Supprime les underscores multiples
    $safeFilename = preg_replace('/_+/', '_', $safeFilename);
    
    // Supprime les underscores en début/fin
    $safeFilename = trim($safeFilename, '_');
    
    // Assure qu'il n'est pas vide
    if (empty($safeFilename)) {
        $safeFilename = 'facture_' . time();
    }
    
    // Limite la longueur (max 200 caractères)
    if (strlen($safeFilename) > 200) {
        $safeFilename = substr($safeFilename, 0, 200);
    }
    
    return $safeFilename;
}
```

### Fonction de Génération (Python)

```python
def generate_safe_filename(invoice_number: str) -> str:
    import re
    
    safe_filename = invoice_number
    
    # Remplace les slashes par des underscores
    safe_filename = safe_filename.replace('/', '_')
    
    # Remplace les autres caractères problématiques
    invalid_chars = ['\\', ':', '*', '?', '"', '<', '>', '|']
    for char in invalid_chars:
        safe_filename = safe_filename.replace(char, '_')
    
    # Supprime les underscores multiples
    safe_filename = re.sub(r'_+', '_', safe_filename)
    
    # Supprime les underscores en début/fin
    safe_filename = safe_filename.strip('_')
    
    # Assure qu'il n'est pas vide
    if not safe_filename:
        from datetime import datetime
        safe_filename = f'facture_{int(datetime.now().timestamp())}'
    
    # Limite la longueur
    if len(safe_filename) > 200:
        safe_filename = safe_filename[:200]
    
    return safe_filename
```

## 📝 Exemples de Conversion

| Numéro de Facture Original | Nom de Fichier Généré | Description |
|----------------------------|------------------------|-------------|
| `FV/2025/0007` | `FV_2025_0007.xml` | Remplacement des slashes |
| `FACT/2025/12/001` | `FACT_2025_12_001.xml` | Multiples slashes |
| `INV-2025-001` | `INV-2025-001.xml` | Tirets conservés |
| `FACTURE\\2025:001` | `FACTURE_2025_001.xml` | Backslash et deux-points |
| `FAC*2025?001` | `FAC_2025_001.xml` | Astérisque et point d'interrogation |
| `FACT"2025<001>` | `FACT_2025_001_.xml` | Guillemets et chevrons |
| `FAC\|2025\|001` | `FAC_2025_001.xml` | Caractères pipe |
| `///FACT///2025///001///` | `FACT_2025_001.xml` | Multiples slashes consécutifs |

## 🚫 Caractères Interdits

Les caractères suivants sont remplacés par des underscores (`_`) :

- `/` (slash) - **Principal caractère à éviter**
- `\` (backslash)
- `:` (deux-points)
- `*` (astérisque)
- `?` (point d'interrogation)
- `"` (guillemets)
- `<` (inférieur)
- `>` (supérieur)
- `|` (pipe)

## ✅ Caractères Autorisés

Les caractères suivants sont conservés :

- Lettres (A-Z, a-z)
- Chiffres (0-9)
- Tirets (`-`)
- Points (`.`)
- Underscores (`_`)
- Espaces (convertis en underscores si nécessaire)

## 🔄 Processus de Génération

1. **Extraction** : Récupération de la valeur `<DocumentIdentifier>` du XML TEIF
2. **Nettoyage** : Remplacement des caractères interdits par des underscores
3. **Optimisation** : Suppression des underscores multiples et en début/fin
4. **Validation** : Vérification que le nom n'est pas vide
5. **Limitation** : Troncature si trop long (max 200 caractères)
6. **Extension** : Ajout de l'extension `.xml`

## 🧪 Tests

### Test Automatique

Utilisez le fichier `test_filename.php` pour tester la génération :

```bash
# Accéder à la page de test
http://localhost:8000/test_filename.php
```

### Test Manuel

```php
// Test simple
$invoiceNumber = "FV/2025/0007";
$filename = generateSafeFilename($invoiceNumber);
echo $filename; // Résultat: FV_2025_0007
```

```python
# Test simple
invoice_number = "FV/2025/0007"
filename = generate_safe_filename(invoice_number)
print(filename)  # Résultat: FV_2025_0007
```

## 📊 Intégration dans l'Application

### Interface Web PHP

1. **Upload PDF** → Extraction des données
2. **Génération XML** → Récupération du `DocumentIdentifier`
3. **Nommage** → Application de `generateSafeFilename()`
4. **Téléchargement** → Fichier avec nom correct

### Script Python

1. **Traitement PDF** → Extraction des données
2. **Génération XML** → Création du TEIF
3. **Sauvegarde** → Utilisation du nom sécurisé

## 🔍 Débogage

### Problèmes Courants

1. **Nom de fichier vide** :
   - Cause : `DocumentIdentifier` manquant ou vide
   - Solution : Génération automatique avec timestamp

2. **Caractères spéciaux** :
   - Cause : Caractères non ASCII dans le numéro
   - Solution : Remplacement par underscores

3. **Nom trop long** :
   - Cause : Numéro de facture très long
   - Solution : Troncature à 200 caractères

### Logs de Débogage

```php
// Ajout de logs pour le débogage
error_log("Original invoice number: " . $invoiceNumber);
error_log("Safe filename generated: " . $safeFilename);
```

## 📋 Conformité

Cette implémentation respecte :

- ✅ **Règle TEIF** : Nom basé sur `DocumentIdentifier`
- ✅ **Sécurité fichiers** : Pas de caractères interdits
- ✅ **Compatibilité OS** : Fonctionne sur Windows/Linux/Mac
- ✅ **Longueur limitée** : Évite les erreurs de système de fichiers
- ✅ **Unicité** : Fallback avec timestamp si nécessaire

## 🔄 Maintenance

### Mise à Jour

Pour modifier les règles de nommage :

1. Modifier `generateSafeFilename()` dans `process.php`
2. Modifier `generate_safe_filename()` dans `ai_studio_code.py`
3. Mettre à jour les tests dans `test_filename.php`
4. Tester avec différents cas d'usage

### Validation

Toujours tester avec :
- Numéros avec slashes multiples
- Caractères spéciaux variés
- Numéros très longs
- Numéros vides ou null
- Caractères Unicode

---

**Version** : 1.0  
**Dernière mise à jour** : 2025  
**Compatibilité** : TEIF v1.8.8, PHP 7.4+, Python 3.7+
