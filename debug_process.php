<?php
require_once 'config.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Debug Process.php</h2>";

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>POST Request Received</h3>";
    
    echo "<h4>POST Data:</h4>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    echo "<h4>FILES Data:</h4>";
    echo "<pre>" . print_r($_FILES, true) . "</pre>";
    
    // Check if file was uploaded
    if (isset($_FILES['pdf_file'])) {
        $uploadedFile = $_FILES['pdf_file'];
        echo "<h4>File Upload Details:</h4>";
        echo "Name: " . $uploadedFile['name'] . "<br>";
        echo "Type: " . $uploadedFile['type'] . "<br>";
        echo "Size: " . $uploadedFile['size'] . " bytes<br>";
        echo "Error: " . $uploadedFile['error'] . "<br>";
        echo "Temp name: " . $uploadedFile['tmp_name'] . "<br>";
        
        if ($uploadedFile['error'] === UPLOAD_ERR_OK) {
            echo "<div style='color: green;'>✓ File uploaded successfully</div>";
            
            // Try to process the file
            echo "<h4>Processing File...</h4>";
            
            try {
                $extractionMode = $_POST['extraction_mode'] ?? 'mock';
                $outputFormat = $_POST['output_format'] ?? 'xml';
                $manualSupplierData = [
                    'Supplier Fiscal ID' => $_POST['supplier_id'] ?? '',
                    'Supplier Name' => $_POST['supplier_name'] ?? '',
                    'Supplier Address' => $_POST['supplier_address'] ?? '',
                ];
                
                echo "Extraction mode: $extractionMode<br>";
                echo "Output format: $outputFormat<br>";
                echo "Manual supplier data: <pre>" . print_r($manualSupplierData, true) . "</pre>";
                
                // Generate unique filename
                $timestamp = time();
                $uniqueId = uniqid();
                $originalName = pathinfo($uploadedFile['name'], PATHINFO_FILENAME);
                $uploadedFileName = $originalName . '_' . $timestamp . '_' . $uniqueId . '.pdf';
                $uploadedFilePath = 'uploads/' . $uploadedFileName;
                
                // Move uploaded file
                if (move_uploaded_file($uploadedFile['tmp_name'], $uploadedFilePath)) {
                    echo "<div style='color: green;'>✓ File moved to: $uploadedFilePath</div>";
                    
                    // Test Python execution
                    echo "<h4>Testing Python Execution...</h4>";
                    
                    $pythonPath = PYTHON_EXECUTABLE;
                    $testCommand = "\"$pythonPath\" --version 2>&1";
                    $output = [];
                    $returnCode = 0;
                    exec($testCommand, $output, $returnCode);
                    
                    echo "Python version command: <code>$testCommand</code><br>";
                    echo "Return code: $returnCode<br>";
                    echo "Output: <pre>" . implode("\n", $output) . "</pre>";
                    
                    if ($returnCode === 0) {
                        echo "<div style='color: green;'>✓ Python is accessible</div>";
                        
                        // Now try to process with our script
                        echo "<h4>Processing with Python Script...</h4>";
                        
                        // Create a simple test Python script
                        $testScript = 'debug_test_' . uniqid() . '.py';
                        $testPythonCode = '
import sys
import os
import json

print("Python script started")
print("Python version:", sys.version)
print("Current directory:", os.getcwd())

# Test if we can import our modules
try:
    import base64
    print("✓ base64 module imported")
except ImportError as e:
    print("✗ base64 import failed:", e)

try:
    import requests
    print("✓ requests module imported")
except ImportError as e:
    print("✗ requests import failed:", e)

try:
    from ai_studio_code import pdf_to_base64
    print("✓ ai_studio_code.pdf_to_base64 imported")
except ImportError as e:
    print("✗ ai_studio_code import failed:", e)

# Test basic functionality
pdf_path = "' . addslashes($uploadedFilePath) . '"
print("PDF path:", pdf_path)

if os.path.exists(pdf_path):
    print("✓ PDF file exists")
    try:
        with open(pdf_path, "rb") as f:
            data = f.read()
        print("✓ PDF file readable, size:", len(data), "bytes")
    except Exception as e:
        print("✗ Error reading PDF:", e)
else:
    print("✗ PDF file does not exist")

print("Script completed successfully")
';
                        
                        file_put_contents($testScript, $testPythonCode);
                        
                        $command = "\"$pythonPath\" \"$testScript\" 2>&1";
                        echo "Command: <code>$command</code><br>";
                        
                        $output = [];
                        $returnCode = 0;
                        exec($command, $output, $returnCode);
                        
                        echo "Return code: $returnCode<br>";
                        echo "Output: <pre>" . implode("\n", $output) . "</pre>";
                        
                        // Clean up
                        unlink($testScript);
                        
                        if ($returnCode === 0) {
                            echo "<div style='color: green;'>✓ Python script executed successfully</div>";
                        } else {
                            echo "<div style='color: red;'>✗ Python script failed</div>";
                        }
                    } else {
                        echo "<div style='color: red;'>✗ Python is not accessible</div>";
                    }
                    
                    // Clean up uploaded file
                    unlink($uploadedFilePath);
                    
                } else {
                    echo "<div style='color: red;'>✗ Failed to move uploaded file</div>";
                }
                
            } catch (Exception $e) {
                echo "<div style='color: red;'>Exception: " . $e->getMessage() . "</div>";
            }
            
        } else {
            echo "<div style='color: red;'>✗ File upload error: " . $uploadedFile['error'] . "</div>";
        }
    } else {
        echo "<div style='color: red;'>✗ No file uploaded</div>";
    }
    
} else {
    echo "<h3>No POST Request</h3>";
    echo "Request method: " . $_SERVER['REQUEST_METHOD'] . "<br>";
    echo "This script expects a POST request with file upload.";
}
?>
