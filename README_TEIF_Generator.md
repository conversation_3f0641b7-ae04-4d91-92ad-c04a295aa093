# TEIF XML Generator for Tunisia Electronic Invoicing

This Python application converts PDF invoices to TEIF (Tunisia Electronic Invoice Format) XML format using AI-powered data extraction.

## Overview

The application implements the complete workflow for Tunisia's mandatory electronic invoicing system:

1. **PDF Processing**: Converts PDF invoices to base64 format
2. **AI Data Extraction**: Uses Google Gemini API to extract invoice data from PDF
3. **TEIF XML Generation**: Creates compliant TEIF v1.8.8 XML format
4. **Validation**: Ensures compliance with Tunisia TradeNet (TTN) standards

## Features

- ✅ **AI-Powered Extraction**: Uses Google Gemini 1.5 Flash for accurate data extraction
- ✅ **TEIF v1.8.8 Compliance**: Generates XML compliant with latest TEIF standard
- ✅ **Multi-Tax Support**: Handles multiple VAT rates (7%, 19%) and fiscal stamps
- ✅ **Error Handling**: Robust error handling and validation
- ✅ **Mock Data Testing**: Built-in test data for development
- ✅ **Flexible Configuration**: Easy switching between AI and mock data

## Requirements

```bash
pip install requests
```

## Configuration

1. **Set up Gemini API Key**:
   ```bash
   export GEMINI_API_KEY="your_api_key_here"
   ```
   Or modify the `GEMINI_API_KEY` variable in the code.

2. **Place your PDF invoice** in the same directory as `ai_studio_code.py`

## Usage

### Basic Usage (with Mock Data)
```bash
python ai_studio_code.py
```

### Using Real AI Extraction
```bash
python ai_studio_code.py --test-ai
```

### Programmatic Usage
```python
from ai_studio_code import pdf_to_base64, extract_invoice_data_with_gemini, generate_teif_xml

# Convert PDF and extract data
base64_data = pdf_to_base64("invoice.pdf")
invoice_data = extract_invoice_data_with_gemini(base64_data)

# Generate TEIF XML
teif_xml = generate_teif_xml(invoice_data)

# Save to file
with open("output.xml", "w", encoding="utf-8") as f:
    f.write(teif_xml)
```

## Output

The application generates:
- **output_teif_invoice.xml**: TEIF XML from mock data
- **ai_extracted_teif.xml**: TEIF XML from AI-extracted data

## TEIF XML Structure

The generated XML follows the official TEIF v1.8.8 structure:

```xml
<TEIF version="1.8.8" controlingAgency="TTN">
  <InvoiceHeader>
    <MessageSenderIdentifier type="I-01">Supplier_ID</MessageSenderIdentifier>
    <MessageRecieverIdentifier type="I-02">Client_ID</MessageRecieverIdentifier>
  </InvoiceHeader>
  <InvoiceBody>
    <Bgm>...</Bgm>
    <Dtm>...</Dtm>
    <PartnerSection>...</PartnerSection>
    <LinSection>...</LinSection>
    <InvoiceMoa>...</InvoiceMoa>
    <InvoiceTax>...</InvoiceTax>
  </InvoiceBody>
</TEIF>
```

## Supported Data Fields

- Invoice Number
- Issue Date (DD/MM/YYYY format)
- Supplier Information (ID, Name, Address)
- Client Information (ID, Name, Address)
- Line Items (Code, Description, Quantity, Unit Price, Tax Rate)
- Tax Calculations (Multiple VAT rates)
- Totals (HT, TTC amounts)
- Fiscal Stamp (Droit de Timbre)
- Amount in Words (French)

## Tax Code Mapping

- **I-1602**: TVA (Value Added Tax)
- **I-1601**: Droit de Timbre (Fiscal Stamp)
- **I-01**: Company Fiscal ID
- **I-02**: Individual CIN (8 digits)

## Error Handling

The application includes comprehensive error handling for:
- PDF file not found
- API connection issues
- JSON parsing errors
- Missing required fields
- Invalid data formats

## Testing

The application includes built-in test data based on the example invoice `FV_2025_0007.pdf` with:
- Solar equipment line items
- Multiple tax rates (7% and 19%)
- Advance payment handling
- Proper TEIF formatting

## Compliance Notes

- Follows TEIF v1.8.8 specification
- Compatible with Tunisia TradeNet (TTN) platform
- Supports El Fatoora electronic invoicing system
- Ready for digital signature integration (future enhancement)

## Future Enhancements

- [ ] Digital signature support (XAdES)
- [ ] Batch processing
- [ ] GUI interface
- [ ] Additional AI providers
- [ ] Validation against XSD schema
- [ ] Direct TTN platform integration

## Support

For issues related to:
- **TEIF Standard**: Refer to official TTN documentation
- **AI Extraction**: Check Gemini API documentation
- **Code Issues**: Review error messages and logs

## License

This code is provided as-is for educational and development purposes.
