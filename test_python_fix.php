<?php
require_once 'config.php';

echo "<h2>Testing Python Configuration Fix</h2>";

// Test 1: Check if Python executable exists
echo "<h3>Test 1: Python Executable Path</h3>";
$pythonPath = PYTHON_EXECUTABLE;
echo "Configured Python path: <code>$pythonPath</code><br>";

if (file_exists($pythonPath)) {
    echo "<span style='color: green;'>✓ Python executable found</span><br>";
} else {
    echo "<span style='color: red;'>✗ Python executable NOT found</span><br>";
}

// Test 2: Try to execute Python version command
echo "<h3>Test 2: Python Version Check</h3>";
$command = "\"$pythonPath\" --version 2>&1";
echo "Command: <code>$command</code><br>";

$output = [];
$returnCode = 0;
exec($command, $output, $returnCode);

echo "Return code: $returnCode<br>";
echo "Output: <pre>" . implode("\n", $output) . "</pre>";

if ($returnCode === 0) {
    echo "<span style='color: green;'>✓ Python is working correctly</span><br>";
} else {
    echo "<span style='color: red;'>✗ Python execution failed</span><br>";
}

// Test 3: Test required Python modules
echo "<h3>Test 3: Required Python Modules</h3>";
$modules = ['requests', 'xml.etree.ElementTree', 'base64', 'json'];

foreach ($modules as $module) {
    $testCommand = "\"$pythonPath\" -c \"import $module; print('$module OK')\" 2>&1";
    $moduleOutput = [];
    $moduleReturnCode = 0;
    exec($testCommand, $moduleOutput, $moduleReturnCode);
    
    if ($moduleReturnCode === 0) {
        echo "<span style='color: green;'>✓ $module</span><br>";
    } else {
        echo "<span style='color: red;'>✗ $module - " . implode(' ', $moduleOutput) . "</span><br>";
    }
}

// Test 4: Test basic Python script execution
echo "<h3>Test 4: Basic Python Script Execution</h3>";
$testScript = 'test_basic.py';
$testPythonCode = '
import sys
import json

print("Python script executed successfully")
print(json.dumps({"status": "success", "message": "Python is working"}))
';

file_put_contents($testScript, $testPythonCode);

$testCommand = "\"$pythonPath\" \"$testScript\" 2>&1";
$testOutput = [];
$testReturnCode = 0;
exec($testCommand, $testOutput, $testReturnCode);

echo "Command: <code>$testCommand</code><br>";
echo "Return code: $testReturnCode<br>";
echo "Output: <pre>" . implode("\n", $testOutput) . "</pre>";

if ($testReturnCode === 0) {
    echo "<span style='color: green;'>✓ Basic Python script execution works</span><br>";
} else {
    echo "<span style='color: red;'>✗ Basic Python script execution failed</span><br>";
}

// Clean up
unlink($testScript);

echo "<h3>Summary</h3>";
if ($returnCode === 0) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<strong>✓ Python configuration appears to be working correctly!</strong><br>";
    echo "The JSON parsing error should now be resolved.";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<strong>✗ Python configuration still has issues.</strong><br>";
    echo "Please check the Python installation and path configuration.";
    echo "</div>";
}
?>
