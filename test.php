<?php
/**
 * Test file to verify the TEIF XML Generator setup
 */

require_once 'config.php';

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-cog"></i> Test de Configuration</h4>
                    </div>
                    <div class="card-body">
                        <h5>Informations de l'Application</h5>
                        <table class="table table-striped">
                            <tr>
                                <td><strong>Nom de l'application:</strong></td>
                                <td><?php echo APP_NAME; ?></td>
                            </tr>
                            <tr>
                                <td><strong>Version:</strong></td>
                                <td><?php echo APP_VERSION; ?></td>
                            </tr>
                            <tr>
                                <td><strong>Version TEIF:</strong></td>
                                <td><?php echo TEIF_VERSION; ?></td>
                            </tr>
                            <tr>
                                <td><strong>Agence de contrôle:</strong></td>
                                <td><?php echo CONTROLLING_AGENCY; ?></td>
                            </tr>
                        </table>

                        <h5 class="mt-4">Configuration PHP</h5>
                        <table class="table table-striped">
                            <tr>
                                <td><strong>Version PHP:</strong></td>
                                <td><?php echo PHP_VERSION; ?></td>
                            </tr>
                            <tr>
                                <td><strong>Taille max upload:</strong></td>
                                <td><?php echo ini_get('upload_max_filesize'); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Taille max POST:</strong></td>
                                <td><?php echo ini_get('post_max_size'); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Limite mémoire:</strong></td>
                                <td><?php echo ini_get('memory_limit'); ?></td>
                            </tr>
                        </table>

                        <h5 class="mt-4">Tests des Répertoires</h5>
                        <table class="table table-striped">
                            <?php
                            $directories = [
                                'Répertoire uploads' => UPLOAD_DIRECTORY,
                                'Répertoire output' => OUTPUT_DIRECTORY,
                                'Répertoire logs' => dirname(LOG_FILE)
                            ];
                            
                            foreach ($directories as $name => $dir) {
                                $exists = file_exists($dir);
                                $writable = $exists && is_writable($dir);
                                $status = $exists ? ($writable ? 'OK' : 'Non accessible en écriture') : 'N\'existe pas';
                                $class = $exists && $writable ? 'text-success' : 'text-danger';
                                echo "<tr><td><strong>$name:</strong></td><td class='$class'>$dir ($status)</td></tr>";
                            }
                            ?>
                        </table>

                        <h5 class="mt-4">Tests des Fichiers</h5>
                        <table class="table table-striped">
                            <?php
                            $files = [
                                'Script Python' => PYTHON_SCRIPT_PATH,
                                'Fichier de test PDF' => 'FV_2025_0007.pdf'
                            ];
                            
                            foreach ($files as $name => $file) {
                                $exists = file_exists($file);
                                $status = $exists ? 'Trouvé' : 'Non trouvé';
                                $class = $exists ? 'text-success' : 'text-warning';
                                echo "<tr><td><strong>$name:</strong></td><td class='$class'>$file ($status)</td></tr>";
                            }
                            ?>
                        </table>

                        <h5 class="mt-4">Test Python</h5>
                        <div class="alert alert-info">
                            <strong>Test de la commande Python:</strong>
                            <?php
                            $pythonTest = shell_exec('python --version 2>&1');
                            if ($pythonTest) {
                                echo "<br><code>$pythonTest</code>";
                                echo "<br><span class='text-success'><i class='fas fa-check'></i> Python est disponible</span>";
                            } else {
                                echo "<br><span class='text-danger'><i class='fas fa-times'></i> Python n'est pas disponible ou non configuré</span>";
                            }
                            ?>
                        </div>

                        <h5 class="mt-4">Configuration IA</h5>
                        <table class="table table-striped">
                            <tr>
                                <td><strong>Extraction IA activée:</strong></td>
                                <td>
                                    <?php if (ENABLE_AI_EXTRACTION): ?>
                                        <span class="text-success"><i class="fas fa-check"></i> Oui</span>
                                    <?php else: ?>
                                        <span class="text-warning"><i class="fas fa-exclamation-triangle"></i> Non (clé API manquante)</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Clé API Gemini:</strong></td>
                                <td>
                                    <?php if (!empty(GEMINI_API_KEY)): ?>
                                        <span class="text-success"><i class="fas fa-check"></i> Configurée</span>
                                    <?php else: ?>
                                        <span class="text-warning"><i class="fas fa-exclamation-triangle"></i> Non configurée</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>

                        <h5 class="mt-4">Extensions PHP Requises</h5>
                        <table class="table table-striped">
                            <?php
                            $extensions = [
                                'fileinfo' => 'Détection du type MIME',
                                'json' => 'Traitement JSON',
                                'curl' => 'Requêtes HTTP (pour IA)'
                            ];
                            
                            foreach ($extensions as $ext => $description) {
                                $loaded = extension_loaded($ext);
                                $status = $loaded ? 'Chargée' : 'Non chargée';
                                $class = $loaded ? 'text-success' : 'text-danger';
                                echo "<tr><td><strong>$ext:</strong></td><td class='$class'>$status - $description</td></tr>";
                            }
                            ?>
                        </table>

                        <div class="mt-4 text-center">
                            <a href="index.php" class="btn btn-primary">
                                <i class="fas fa-arrow-left"></i> Retour à l'application
                            </a>
                            <button class="btn btn-success" onclick="runQuickTest()">
                                <i class="fas fa-play"></i> Test rapide
                            </button>
                        </div>

                        <div id="testResult" class="mt-3" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function runQuickTest() {
            const resultDiv = document.getElementById('testResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> Test en cours...</div>';
            
            // Simulate a quick test
            setTimeout(() => {
                const allGood = <?php 
                    echo json_encode(
                        file_exists(PYTHON_SCRIPT_PATH) && 
                        file_exists('FV_2025_0007.pdf') && 
                        is_writable(UPLOAD_DIRECTORY) && 
                        extension_loaded('fileinfo')
                    ); 
                ?>;
                
                if (allGood) {
                    resultDiv.innerHTML = '<div class="alert alert-success"><i class="fas fa-check"></i> Tous les tests sont passés! L\'application est prête à être utilisée.</div>';
                } else {
                    resultDiv.innerHTML = '<div class="alert alert-warning"><i class="fas fa-exclamation-triangle"></i> Certains éléments nécessitent votre attention. Vérifiez les éléments en rouge ci-dessus.</div>';
                }
            }, 2000);
        }
    </script>
</body>
</html>
