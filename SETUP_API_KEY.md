# Configuration de la Clé API Gemini

## 🔑 Obtenir votre Clé API

1. **Visitez Google AI Studio** : https://makersuite.google.com/app/apikey
2. **Connectez-vous** avec votre compte Google
3. **Créez une nouvelle clé API**
4. **Copiez la clé** (elle commence généralement par `AIza...`)

## 📝 Méthodes de Configuration

### **Méthode 1 : Variable d'Environnement (Recommandée)**

#### Windows PowerShell
```powershell
# Définir la variable pour la session actuelle
$env:GEMINI_API_KEY="AIzaSyC-votre_cle_api_ici"

# Démarrer le serveur
php -S localhost:8000
```

#### Windows Command Prompt
```cmd
# Définir la variable pour la session actuelle
set GEMINI_API_KEY=AIzaSyC-votre_cle_api_ici

# Démarrer le serveur
php -S localhost:8000
```

#### Linux/Mac
```bash
# Définir la variable pour la session actuelle
export GEMINI_API_KEY="AIzaSyC-votre_cle_api_ici"

# Démarrer le serveur
php -S localhost:8000
```

### **Méthode 2 : Fichier .env (Professionnel)**

1. **Copiez le fichier exemple** :
   ```bash
   copy .env.example .env
   ```

2. **Éditez le fichier .env** :
   ```env
   GEMINI_API_KEY=AIzaSyC-votre_cle_api_ici
   ```

3. **Démarrez le serveur** :
   ```bash
   php -S localhost:8000
   ```

### **Méthode 3 : Configuration Directe (Moins Sécurisée)**

1. **Ouvrez `config.php`**
2. **Trouvez cette ligne** :
   ```php
   // define('GEMINI_API_KEY', 'your_actual_api_key_here');
   ```
3. **Décommentez et remplacez** :
   ```php
   define('GEMINI_API_KEY', 'AIzaSyC-votre_cle_api_ici');
   ```

## ✅ Vérification de la Configuration

### **Test via l'Interface Web**

1. Accédez à : `http://localhost:8000/test.php`
2. Vérifiez la section "Configuration IA"
3. Vous devriez voir : ✅ **Clé API Gemini : Configurée**

### **Test via Script PHP**

```php
<?php
require_once 'config.php';

if (ENABLE_AI_EXTRACTION) {
    echo "✅ Clé API configurée et IA activée\n";
    echo "Clé (masquée): " . substr(GEMINI_API_KEY, 0, 10) . "...\n";
} else {
    echo "❌ Clé API non configurée\n";
}
?>
```

## 🔒 Sécurité

### **Bonnes Pratiques**

1. **Ne jamais commiter** la clé API dans le code source
2. **Utiliser des variables d'environnement** en production
3. **Restreindre l'accès** au fichier .env (permissions 600)
4. **Régénérer la clé** si elle est compromise

### **Protection du Fichier .env**

```bash
# Linux/Mac - Restreindre les permissions
chmod 600 .env

# Ajouter au .gitignore
echo ".env" >> .gitignore
```

## 🚨 Dépannage

### **Problème : "Clé API non configurée"**

**Solutions :**
1. Vérifiez que la variable d'environnement est définie
2. Redémarrez le serveur PHP après avoir défini la variable
3. Vérifiez l'orthographe : `GEMINI_API_KEY` (sensible à la casse)

### **Problème : "API Key Invalid"**

**Solutions :**
1. Vérifiez que la clé est complète (commence par `AIza`)
2. Régénérez une nouvelle clé sur Google AI Studio
3. Vérifiez que l'API Gemini est activée pour votre projet

### **Problème : "Quota Exceeded"**

**Solutions :**
1. Vérifiez vos limites sur Google AI Studio
2. Attendez la réinitialisation du quota
3. Utilisez le mode "mock" pour les tests

## 📊 Modes de Fonctionnement

### **Avec Clé API (Mode IA)**
- ✅ Extraction réelle des données PDF
- ✅ Reconnaissance OCR avancée
- ✅ Traitement intelligent des factures

### **Sans Clé API (Mode Mock)**
- ✅ Données de test prédéfinies
- ✅ Génération XML fonctionnelle
- ✅ Tests et développement

## 🔄 Changement de Clé

Pour changer votre clé API :

1. **Méthode Variable d'Environnement** :
   ```bash
   export GEMINI_API_KEY="nouvelle_cle_ici"
   ```

2. **Méthode Fichier .env** :
   ```env
   GEMINI_API_KEY=nouvelle_cle_ici
   ```

3. **Redémarrez le serveur** pour appliquer les changements

## 📈 Monitoring

### **Vérification du Statut**

```php
// Dans votre code PHP
if (ENABLE_AI_EXTRACTION) {
    logMessage('INFO', 'AI extraction enabled with API key');
} else {
    logMessage('WARNING', 'AI extraction disabled - no API key');
}
```

### **Logs d'Utilisation**

Les appels à l'API sont automatiquement loggés dans `logs/app.log` :
- Succès des extractions
- Erreurs d'API
- Utilisation du quota

## 🆘 Support

### **Ressources Utiles**

- **Documentation Gemini** : https://ai.google.dev/docs
- **Console Google Cloud** : https://console.cloud.google.com/
- **Limites et Quotas** : https://ai.google.dev/pricing

### **Contact Support**

Si vous rencontrez des problèmes :
1. Vérifiez les logs dans `logs/app.log`
2. Testez avec le mode mock d'abord
3. Vérifiez votre quota sur Google AI Studio

---

**Important** : Gardez votre clé API secrète et ne la partagez jamais publiquement !
