<?php
/**
 * Test script to verify filename generation from invoice numbers
 */

// Include the function from process.php
function generateSafeFilename($invoiceNumber) {
    // Remove or replace invalid characters for filenames
    $safeFilename = $invoiceNumber;
    
    // Replace forward slashes with underscores
    $safeFilename = str_replace('/', '_', $safeFilename);
    
    // Replace other potentially problematic characters
    $safeFilename = str_replace(['\\', ':', '*', '?', '"', '<', '>', '|'], '_', $safeFilename);
    
    // Remove multiple consecutive underscores
    $safeFilename = preg_replace('/_+/', '_', $safeFilename);
    
    // Remove leading/trailing underscores
    $safeFilename = trim($safeFilename, '_');
    
    // Ensure filename is not empty
    if (empty($safeFilename)) {
        $safeFilename = 'facture_' . time();
    }
    
    // Limit filename length (Windows has 255 char limit, leave room for extension)
    if (strlen($safeFilename) > 200) {
        $safeFilename = substr($safeFilename, 0, 200);
    }
    
    return $safeFilename;
}

// Test cases
$testCases = [
    'FV/2025/0007' => 'FV_2025_0007',
    'FACT/2025/12/001' => 'FACT_2025_12_001',
    'INV-2025-001' => 'INV-2025-001',
    'FACTURE\\2025:001' => 'FACTURE_2025_001',
    'FAC*2025?001' => 'FAC_2025_001',
    'FACT"2025<001>' => 'FACT_2025_001_',
    'FAC|2025|001' => 'FAC_2025_001',
    '///FACT///2025///001///' => 'FACT_2025_001',
    '' => 'facture_' . time(),
    'VERY_LONG_INVOICE_NUMBER_THAT_EXCEEDS_THE_MAXIMUM_LENGTH_ALLOWED_FOR_FILENAMES_AND_SHOULD_BE_TRUNCATED_TO_PREVENT_FILESYSTEM_ERRORS_WHEN_SAVING_FILES_WITH_EXTREMELY_LONG_NAMES_THAT_COULD_CAUSE_ISSUES_ON_VARIOUS_OPERATING_SYSTEMS' => substr('VERY_LONG_INVOICE_NUMBER_THAT_EXCEEDS_THE_MAXIMUM_LENGTH_ALLOWED_FOR_FILENAMES_AND_SHOULD_BE_TRUNCATED_TO_PREVENT_FILESYSTEM_ERRORS_WHEN_SAVING_FILES_WITH_EXTREMELY_LONG_NAMES_THAT_COULD_CAUSE_ISSUES_ON_VARIOUS_OPERATING_SYSTEMS', 0, 200)
];

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Génération de noms de fichiers</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-case {
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        .original {
            background-color: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            font-family: monospace;
        }
        .result {
            background-color: #d4edda;
            padding: 5px;
            border-radius: 3px;
            font-family: monospace;
            color: #155724;
        }
        .success {
            color: #28a745;
        }
        .warning {
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-file-signature"></i> Test de Génération de Noms de Fichiers</h4>
                        <p class="mb-0 small">Vérification de la conversion des numéros de facture en noms de fichiers valides</p>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <strong>Règle :</strong> Le nom de fichier doit être basé sur la valeur de la balise « DocumentIdentifier » 
                            sans le caractère '/' et autres caractères interdits dans les noms de fichiers.
                        </div>

                        <h5>Résultats des Tests</h5>
                        
                        <?php foreach ($testCases as $original => $expected): ?>
                            <?php 
                            $result = generateSafeFilename($original);
                            $isCorrect = ($result === $expected) || (empty($original) && strpos($result, 'facture_') === 0);
                            ?>
                            <div class="test-case">
                                <div class="row align-items-center">
                                    <div class="col-md-4">
                                        <strong>Original :</strong><br>
                                        <span class="original"><?php echo htmlspecialchars($original ?: '(vide)'); ?></span>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Résultat :</strong><br>
                                        <span class="result"><?php echo htmlspecialchars($result); ?>.xml</span>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <?php if ($isCorrect): ?>
                                            <span class="badge bg-success">✓ Correct</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning">⚠ Différent</span><br>
                                            <small class="text-muted">Attendu: <?php echo htmlspecialchars($expected); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <div class="mt-4">
                            <h5>Caractères Remplacés</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Caractères interdits :</h6>
                                    <ul class="list-unstyled">
                                        <li><code>/</code> → <code>_</code> (slash)</li>
                                        <li><code>\</code> → <code>_</code> (backslash)</li>
                                        <li><code>:</code> → <code>_</code> (deux-points)</li>
                                        <li><code>*</code> → <code>_</code> (astérisque)</li>
                                        <li><code>?</code> → <code>_</code> (point d'interrogation)</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>Autres caractères :</h6>
                                    <ul class="list-unstyled">
                                        <li><code>"</code> → <code>_</code> (guillemets)</li>
                                        <li><code>&lt;</code> → <code>_</code> (inférieur)</li>
                                        <li><code>&gt;</code> → <code>_</code> (supérieur)</li>
                                        <li><code>|</code> → <code>_</code> (pipe)</li>
                                        <li>Underscores multiples → <code>_</code> unique</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <h5>Exemples Pratiques</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Numéro de Facture</th>
                                            <th>Nom de Fichier XML</th>
                                            <th>Statut</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $practicalExamples = [
                                            'FV/2025/0007',
                                            'FACT/2025/12/001',
                                            'INV-2025-001',
                                            'FACTURE_2025_001',
                                            'FAC.2025.001'
                                        ];
                                        
                                        foreach ($practicalExamples as $example):
                                            $filename = generateSafeFilename($example);
                                        ?>
                                        <tr>
                                            <td><code><?php echo htmlspecialchars($example); ?></code></td>
                                            <td><code><?php echo htmlspecialchars($filename); ?>.xml</code></td>
                                            <td><span class="badge bg-success">Valide</span></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="mt-4 text-center">
                            <a href="index.php" class="btn btn-primary">
                                <i class="fas fa-arrow-left"></i> Retour à l'application
                            </a>
                            <a href="test.php" class="btn btn-secondary">
                                <i class="fas fa-cog"></i> Tests système
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
