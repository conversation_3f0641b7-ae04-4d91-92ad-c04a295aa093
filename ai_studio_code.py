import requests
import xml.etree.ElementTree as ET
from xml.dom import minidom
from datetime import datetime
import base64
import json
import os
from typing import Dict, List, Optional

# --- Configuration ---
# Replace with your actual API key - preferably from environment variable
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "AIzaSyCvvzWhEC0ibjDtx5orS-OAVdn5DLJT5eM")
GEMINI_API_ENDPOINT = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"

# --- Part 1a: PDF to Base64 (for AI API) ---
def pdf_to_base64(pdf_path):
    """Reads a PDF file and encodes it to a base64 string."""
    try:
        with open(pdf_path, "rb") as pdf_file:
            encoded_string = base64.b64encode(pdf_file.read()).decode('utf-8')
        return encoded_string
    except FileNotFoundError:
        print(f"Error: PDF file not found at {pdf_path}")
        return None
    except Exception as e:
        print(f"Error encoding PDF: {e}")
        return None

# --- Part 1b: Send to AI API and Get Data ---
def extract_invoice_data_with_gemini(base64_pdf_data: str) -> Optional[Dict]:
    """
    Sends the base64 encoded PDF to Gemini API for data extraction.
    Returns a dictionary of extracted invoice data.
    """
    headers = {
        "Content-Type": "application/json",
    }

    prompt = """
    Analyze this PDF invoice and extract the following data. Return ONLY a valid JSON object with these exact keys:

    CRITICAL INSTRUCTIONS:
    - CLIENT/CUSTOMER information must be extracted from the dedicated client information box (usually on the right side)
    - DO NOT use header/letterhead text as client information
    - Look for labels like "Code Client", "Raison Sociale", "Client:" to identify the actual customer
    - The SUPPLIER is the company issuing the invoice (usually in letterhead/left side)

    {
        "Invoice Number": "string - invoice number from document",
        "Issue Date": "string - date in DD/MM/YYYY format",
        "Supplier Fiscal ID": "string - supplier tax ID/matricule",
        "Supplier Name": "string - supplier company name",
        "Supplier Address": "string - full supplier address",
        "Client Fiscal ID": "string - client tax ID/CIN (from client info box, NOT header)",
        "Client Name": "string - client name (from client info box, NOT header)",
        "Client Address": "string - full client address (from client info box)",
        "Line Items": [
            {
                "ItemCode": "string - product code if available",
                "Description": "string - product/service description",
                "Quantity": "number - quantity",
                "UnitPrice": "number - unit price",
                "TaxRate": "number - tax rate percentage (e.g., 7, 19)"
            }
        ],
        "Total HT Amount": "number - total before tax",
        "Total TTC Amount": "number - total including tax",
        "Stamp Duty": "number - fiscal stamp amount",
        "Amount in Words": "string - amount written in words in French"
    }

    Extract all numerical values as numbers (not strings with commas). For dates, use DD/MM/YYYY format.
    Pay special attention to distinguish between header/letterhead and actual client information boxes.
    """

    # Gemini API request structure for PDF processing
    data = {
        "contents": [
            {
                "parts": [
                    {"text": prompt},
                    {
                        "inline_data": {
                            "mime_type": "application/pdf",
                            "data": base64_pdf_data
                        }
                    }
                ]
            }
        ],
        "generationConfig": {
            "temperature": 0.1,
            "topK": 1,
            "topP": 1,
            "maxOutputTokens": 4096,
        }
    }

    try:
        print("Sending request to Gemini API...")
        response = requests.post(
            f"{GEMINI_API_ENDPOINT}?key={GEMINI_API_KEY}",
            json=data,
            headers=headers,
            timeout=60
        )
        response.raise_for_status()

        response_json = response.json()
        print("Received response from Gemini API")

        # Extract the AI's response text
        if "candidates" in response_json and len(response_json["candidates"]) > 0:
            ai_output_text = response_json["candidates"][0]["content"]["parts"][0]["text"]
            print(f"AI Raw Output: {ai_output_text[:500]}...")

            # Clean the response - remove markdown code blocks if present
            cleaned_text = ai_output_text.strip()
            if cleaned_text.startswith("```json"):
                cleaned_text = cleaned_text[7:]
            if cleaned_text.endswith("```"):
                cleaned_text = cleaned_text[:-3]
            cleaned_text = cleaned_text.strip()

            # Parse as JSON
            extracted_data = json.loads(cleaned_text)
            print("Successfully extracted invoice data from AI")
            return extracted_data
        else:
            print("No candidates found in AI response")
            return None

    except requests.exceptions.HTTPError as errh:
        error_msg = f"Erreur HTTP {errh.response.status_code}: {errh}"
        if hasattr(errh.response, 'text'):
            try:
                error_response = json.loads(errh.response.text)
                if 'error' in error_response:
                    if errh.response.status_code == 404:
                        error_msg = "❌ Modèle Gemini non trouvé. Vérifiez votre clé API et les permissions."
                    elif errh.response.status_code == 403:
                        error_msg = "❌ Accès refusé à l'API Gemini. Vérifiez votre clé API et les quotas."
                    elif errh.response.status_code == 429:
                        error_msg = "❌ Limite de taux dépassée. Attendez quelques minutes avant de réessayer."
                    else:
                        error_msg = f"❌ Erreur API Gemini: {error_response['error'].get('message', 'Erreur inconnue')}"
            except:
                error_msg = f"❌ Erreur HTTP {errh.response.status_code}: {errh.response.text[:200]}"
        print(error_msg)
        raise Exception(error_msg)
    except requests.exceptions.ConnectionError as errc:
        error_msg = "❌ Erreur de connexion: Impossible de se connecter à l'API Gemini. Vérifiez votre connexion internet."
        print(error_msg)
        raise Exception(error_msg)
    except requests.exceptions.Timeout as errt:
        error_msg = "❌ Timeout: L'API Gemini met trop de temps à répondre. Réessayez plus tard."
        print(error_msg)
        raise Exception(error_msg)
    except requests.exceptions.RequestException as err:
        error_msg = f"❌ Erreur de requête: {err}"
        print(error_msg)
        raise Exception(error_msg)
    except json.JSONDecodeError as e:
        error_msg = f"❌ Erreur de parsing JSON: La réponse de l'IA n'est pas au format JSON valide. {e}"
        print(error_msg)
        print(f"Texte problématique: {cleaned_text if 'cleaned_text' in locals() else 'N/A'}")
        raise Exception(error_msg)
    except KeyError as e:
        error_msg = f"❌ Structure de réponse inattendue: {e}"
        print(error_msg)
        raise Exception(error_msg)
    except Exception as e:
        error_msg = f"❌ Erreur inattendue lors de l'extraction: {e}"
        print(error_msg)
        raise Exception(error_msg)

# --- Part 1c: Generate TEIF XML ---

# Helper function for XML formatting
def _prettify(elem):
    """Return a pretty-printed XML string for the Element."""
    rough_string = ET.tostring(elem, 'utf-8')
    reparsed = minidom.parseString(rough_string)
    return reparsed.toprettyxml(indent="  ")

def generate_safe_filename(invoice_number: str) -> str:
    """
    Generate a safe filename from invoice number by removing/replacing invalid characters.
    """
    import re

    # Start with the invoice number
    safe_filename = invoice_number

    # Replace forward slashes with underscores
    safe_filename = safe_filename.replace('/', '_')

    # Replace other potentially problematic characters
    invalid_chars = ['\\', ':', '*', '?', '"', '<', '>', '|']
    for char in invalid_chars:
        safe_filename = safe_filename.replace(char, '_')

    # Remove multiple consecutive underscores
    safe_filename = re.sub(r'_+', '_', safe_filename)

    # Remove leading/trailing underscores
    safe_filename = safe_filename.strip('_')

    # Ensure filename is not empty
    if not safe_filename:
        from datetime import datetime
        safe_filename = f'facture_{int(datetime.now().timestamp())}'

    # Limit filename length (leave room for extension)
    if len(safe_filename) > 200:
        safe_filename = safe_filename[:200]

    return safe_filename

def generate_teif_xml(invoice_data: Dict, manual_supplier_data: Optional[Dict] = None) -> str:
    """
    Generates the TEIF XML structure from extracted invoice data.
    Based on TEIF v1.8.8 standard format.
    """

    # If manual supplier data is provided and has a fiscal ID, it overrides the invoice_data
    if manual_supplier_data and manual_supplier_data.get("Supplier Fiscal ID"):
        invoice_data["Supplier Fiscal ID"] = manual_supplier_data["Supplier Fiscal ID"]
        invoice_data["Supplier Name"] = manual_supplier_data.get("Supplier Name", "")
        invoice_data["Supplier Address"] = manual_supplier_data.get("Supplier Address", "")

    # 1. Root Element - using version 1.8.8 as per the standard
    teif = ET.Element("TEIF", version="1.8.8", controlingAgency="TTN")

    # 2. Invoice Header
    header = ET.SubElement(teif, "InvoiceHeader")
    supplier_id = invoice_data.get("Supplier Fiscal ID") or "UNKNOWN_SUPPLIER"
    ET.SubElement(header, "MessageSenderIdentifier", type="I-01").text = supplier_id

    # Client identifier - use I-02 for CIN (individual) or I-01 for fiscal ID (company)
    client_id = invoice_data.get("Client Fiscal ID", "")
    if client_id is None:
        client_id = "00000000"
    client_id_type = "I-02" if len(client_id) == 8 else "I-01"
    ET.SubElement(header, "MessageRecieverIdentifier", type=client_id_type).text = client_id

    # 3. Invoice Body
    body = ET.SubElement(teif, "InvoiceBody")

    # Bgm (Beginning Message)
    bgm = ET.SubElement(body, "Bgm")
    ET.SubElement(bgm, "DocumentIdentifier").text = invoice_data["Invoice Number"]
    ET.SubElement(bgm, "DocumentType", code="I-11").text = "Facture"

    # Dtm (Date/Period) - convert date format if needed
    dtm = ET.SubElement(body, "Dtm")
    issue_date = invoice_data["Issue Date"]
    # Convert DD/MM/YYYY to DDMMYY format for TEIF
    if "/" in issue_date:
        day, month, year = issue_date.split("/")
        formatted_date = f"{day}{month}{year[-2:]}"
    else:
        formatted_date = issue_date
    ET.SubElement(dtm, "DateText", functionCode="I-31", format="DDMMYY").text = formatted_date

    # Partner Section
    partner_section = ET.SubElement(body, "PartnerSection")

    # Supplier Details (Emetteur - I-66)
    supplier_details = ET.SubElement(partner_section, "PartnerDetails", functionCode="I-66")
    supplier_nad = ET.SubElement(supplier_details, "Nad")
    ET.SubElement(supplier_nad, "PartnerIdentifier", type="I-01").text = supplier_id
    ET.SubElement(supplier_nad, "PartnerName", nameType="Qualification").text = invoice_data.get("Supplier Name", "UNKNOWN_SUPPLIER")

    supplier_addresses = ET.SubElement(supplier_nad, "PartnerAdresses", lang="fr")
    ET.SubElement(supplier_addresses, "AdressDescription").text = invoice_data.get("Supplier Address", "")
    ET.SubElement(supplier_addresses, "Country", codeList="ISO_3166-1").text = "TN"

    # Client Details (Acheteur - I-64)
    client_details = ET.SubElement(partner_section, "PartnerDetails", functionCode="I-64")
    client_nad = ET.SubElement(client_details, "Nad")
    ET.SubElement(client_nad, "PartnerIdentifier", type=client_id_type).text = invoice_data["Client Fiscal ID"]
    ET.SubElement(client_nad, "PartnerName", nameType="I-72").text = invoice_data["Client Name"]

    client_addresses = ET.SubElement(client_nad, "PartnerAdresses", lang="fr")
    ET.SubElement(client_addresses, "AdressDescription").text = invoice_data.get("Client Address", "")
    ET.SubElement(client_addresses, "Country", codeList="ISO_3166-1").text = "TN"

    # Line Item Section
    lin_section = ET.SubElement(body, "LinSection")
    for item in invoice_data["Line Items"]:
        lin = ET.SubElement(lin_section, "Lin")

        # Item identifier (product code)
        if "ItemCode" in item and item["ItemCode"]:
            ET.SubElement(lin, "ItemIdentifier").text = item["ItemCode"]

        # Line description
        lin_imd = ET.SubElement(lin, "LinImd")
        ET.SubElement(lin_imd, "ItemDescription").text = item["Description"]

        # Quantity
        lin_qty = ET.SubElement(lin, "LinQty")
        ET.SubElement(lin_qty, "Quantity", measurementUnit="EA").text = f"{item['Quantity']:.4f}"

        # Tax information
        lin_tax = ET.SubElement(lin, "LinTax")
        ET.SubElement(lin_tax, "TaxTypeName", code="I-1602")  # TVA
        tax_details = ET.SubElement(lin_tax, "TaxDetails")
        ET.SubElement(tax_details, "TaxRate").text = f"{item['TaxRate']:.2f}"

        # Line monetary amount
        lin_moa = ET.SubElement(lin, "LinMoa")
        moa_details = ET.SubElement(lin_moa, "MoaDetails")
        moa = ET.SubElement(moa_details, "Moa",
                           currencyCodeList="ISO_4217",
                           amountTypeCode="I-171")
        line_total = item['Quantity'] * item['UnitPrice']
        amount_elem = ET.SubElement(moa, "Amount", currencyIdentifier="TND")
        amount_elem.text = f"{line_total:.3f}"

    # Invoice Monetary Amounts
    invoice_moa = ET.SubElement(body, "InvoiceMoa")

    # Total HT (before tax)
    amount_details_ht = ET.SubElement(invoice_moa, "AmountDetails")
    moa_ht = ET.SubElement(amount_details_ht, "Moa",
                          currencyCodeList="ISO_4217",
                          amountTypeCode="I-176")
    ET.SubElement(moa_ht, "Amount", currencyIdentifier="TND").text = f"{invoice_data['Total HT Amount']:.3f}"

    # Total TTC (including tax)
    amount_details_ttc = ET.SubElement(invoice_moa, "AmountDetails")
    moa_ttc = ET.SubElement(amount_details_ttc, "Moa",
                           currencyCodeList="ISO_4217",
                           amountTypeCode="I-180")
    amount_ttc = ET.SubElement(moa_ttc, "Amount", currencyIdentifier="TND")
    amount_ttc.text = f"{invoice_data['Total TTC Amount']:.3f}"

    # Amount in words
    if "Amount in Words" in invoice_data and invoice_data["Amount in Words"]:
        ET.SubElement(moa_ttc, "AmountDescription", lang="FR").text = invoice_data["Amount in Words"]

    # Invoice Tax Section
    invoice_tax = ET.SubElement(body, "InvoiceTax")

    # Calculate tax amounts by rate
    tax_amounts = {}
    for item in invoice_data["Line Items"]:
        rate = item["TaxRate"]
        line_total = item['Quantity'] * item['UnitPrice']
        if rate not in tax_amounts:
            tax_amounts[rate] = {"base": 0, "tax": 0}
        tax_amounts[rate]["base"] += line_total
        tax_amounts[rate]["tax"] += line_total * (rate / 100)

    # Add tax details for each rate
    for rate, amounts in tax_amounts.items():
        if rate > 0:  # Only add if there's actual tax
            tax_details = ET.SubElement(invoice_tax, "InvoiceTaxDetails")
            tax = ET.SubElement(tax_details, "Tax")
            ET.SubElement(tax, "TaxTypeName", code="I-1602")  # TVA
            tax_detail = ET.SubElement(tax, "TaxDetails")
            ET.SubElement(tax_detail, "TaxRate").text = f"{rate:.2f}"

            moa_details = ET.SubElement(tax_details, "MoaDetails")
            # Tax base amount
            moa_base = ET.SubElement(moa_details, "Moa",
                                   currencyCodeList="ISO_4217",
                                   amountTypeCode="I-177")
            ET.SubElement(moa_base, "Amount", currencyIdentifier="TND").text = f"{amounts['base']:.3f}"

            # Tax amount
            moa_tax = ET.SubElement(moa_details, "Moa",
                                  currencyCodeList="ISO_4217",
                                  amountTypeCode="I-178")
            ET.SubElement(moa_tax, "Amount", currencyIdentifier="TND").text = f"{amounts['tax']:.3f}"

    # Stamp duty (fiscal stamp)
    if invoice_data.get("Stamp Duty", 0) > 0:
        stamp_tax_details = ET.SubElement(invoice_tax, "InvoiceTaxDetails")
        stamp_tax = ET.SubElement(stamp_tax_details, "Tax")
        ET.SubElement(stamp_tax, "TaxTypeName", code="I-1601")  # Droit de timbre

        stamp_moa_details = ET.SubElement(stamp_tax_details, "MoaDetails")
        stamp_moa = ET.SubElement(stamp_moa_details, "Moa",
                                 currencyCodeList="ISO_4217",
                                 amountTypeCode="I-178")
        ET.SubElement(stamp_moa, "Amount", currencyIdentifier="TND").text = f"{invoice_data['Stamp Duty']:.3f}"

    return _prettify(teif)


# --- Main Execution Flow ---
def main():
    """Main function to process PDF invoice and generate TEIF XML"""

    # --- MANUAL SUPPLIER DATA ---
    # Fill in your company's details here. If "Supplier Fiscal ID" is filled, these details will override AI extraction for the supplier.
    manual_supplier_data = {
        "Supplier Fiscal ID": "1327372W",      # e.g., "1234567X"
        "Supplier Name": "SOLAR ENERGY SOLUTIONS",          # e.g., "My Company SARL"
        "Supplier Address": "Zone Industrielle, Sfax, Tunisia"    # e.g., "123 Main Street, Tunis, Tunisia"
    }

    # Configuration
    invoice_pdf_path = "Exemple_facture_qr.pdf"
    use_ai_extraction = False  # Set to True to use actual AI API

    print("=== TEIF XML Generator ===")
    print(f"Processing invoice: {invoice_pdf_path}")

    # Step 1: Convert PDF to Base64
    print("\n1. Converting PDF to Base64...")
    base64_data = pdf_to_base64(invoice_pdf_path)
    if not base64_data:
        print("❌ Failed to convert PDF to Base64.")
        return

    print("✅ PDF converted to Base64 successfully")

    # Step 2: Extract invoice data
    print("\n2. Extracting invoice data...")

    if use_ai_extraction and GEMINI_API_KEY:
        print("Using Gemini AI for data extraction...")
        extracted_invoice_data = extract_invoice_data_with_gemini(base64_data)
    else:
        print("Using mock data for testing...")
        # Mock data based on the guide.txt example
        extracted_invoice_data = {
            "Invoice Number": "FV/2025/0007",
            "Issue Date": "06/02/2025",
            "Supplier Fiscal ID": "1327372W",
            "Supplier Name": "SOLAR ENERGY SOLUTIONS",
            "Supplier Address": "Zone Industrielle, Sfax, Tunisia",
            "Client Fiscal ID": "08777647",
            "Client Name": "EMNA KACEM",
            "Client Address": "RTE SOKRA KM 5, Sfax Sud, Tunisia",
            "Line Items": [
                {
                    "ItemCode": "**********",
                    "Description": "MODULE PHOTOVOLTAIQUE JA SOLAR MONO 565 Wc",
                    "Quantity": 6.0,
                    "UnitPrice": 540.0,
                    "TaxRate": 7.0
                },
                {
                    "ItemCode": "**********",
                    "Description": "ONDULEUR SOLIS MONO 3 KW",
                    "Quantity": 1.0,
                    "UnitPrice": 1870.0,
                    "TaxRate": 7.0
                },
                {
                    "ItemCode": "**********",
                    "Description": "INSTALLATION ET MISE EN SERVICE",
                    "Quantity": 1.0,
                    "UnitPrice": 1000.0,
                    "TaxRate": 19.0
                },
                {
                    "ItemCode": "ADV-001",
                    "Description": "ACOMPTE RECU",
                    "Quantity": 1.0,
                    "UnitPrice": -1000.0,
                    "TaxRate": 19.0
                }
            ],
            "Total HT Amount": 4332.0,
            "Total TTC Amount": 4542.88,
            "Stamp Duty": 1.0,
            "Amount in Words": "Quatre Mille Cinq Cent Quarante-Deux Dinar et Huit Cent Quatre-Vingts Millimes"
        }

    if not extracted_invoice_data:
        print("❌ Failed to extract invoice data")
        return

    print("✅ Invoice data extracted successfully")
    print(f"   Invoice Number: {extracted_invoice_data.get('Invoice Number')}")
    print(f"   Client: {extracted_invoice_data.get('Client Name')}")
    print(f"   Total TTC: {extracted_invoice_data.get('Total TTC Amount')} TND")

    # Step 3: Generate TEIF XML
    print("\n3. Generating TEIF XML...")
    try:
        teif_xml_output = generate_teif_xml(extracted_invoice_data, manual_supplier_data)

        # Generate safe filename based on invoice number
        invoice_number = extracted_invoice_data.get('Invoice Number', 'facture_unknown')
        safe_filename = generate_safe_filename(invoice_number)
        output_filename = f"{safe_filename}.xml"

        with open(output_filename, "w", encoding="utf-8") as f:
            f.write(teif_xml_output)

        print(f"✅ TEIF XML generated successfully")
        print(f"   Invoice Number: {invoice_number}")
        print(f"   Safe Filename: {safe_filename}")
        print(f"   Saved to: {output_filename}")
        print(f"   File size: {len(teif_xml_output)} characters")

        # Display preview
        print("\n📄 XML Preview (first 800 characters):")
        print("-" * 60)
        print(teif_xml_output[:800] + "..." if len(teif_xml_output) > 800 else teif_xml_output)
        print("-" * 60)

        return True

    except Exception as e:
        print(f"❌ Failed to generate TEIF XML: {e}")
        return False

def test_with_real_ai():
    """Test function to use real Gemini AI API"""
    invoice_pdf_path = "Exemple_facture_qr.pdf"

    print("=== Testing with Real AI API ===")

    # Convert PDF to Base64
    base64_data = pdf_to_base64(invoice_pdf_path)
    if not base64_data:
        print("❌ Failed to convert PDF")
        return

    # Extract with AI
    extracted_data = extract_invoice_data_with_gemini(base64_data)
    if extracted_data:
        print("✅ AI extraction successful!")
        print(json.dumps(extracted_data, indent=2, ensure_ascii=False))

        # Generate XML
        xml_output = generate_teif_xml(extracted_data)
        with open("ai_extracted_teif.xml", "w", encoding="utf-8") as f:
            f.write(xml_output)
        print("✅ XML saved to ai_extracted_teif.xml")
    else:
        print("❌ AI extraction failed")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--test-ai":
        test_with_real_ai()
    else:
        main()