# Interface Web PHP - Générateur TEIF XML

Interface web complète pour convertir les factures PDF au format TEIF XML pour la facturation électronique tunisienne.

## 🌟 Fonctionnalités

- **Interface utilisateur moderne** avec Bootstrap 5
- **Glisser-déposer** de fichiers PDF
- **Extraction IA** avec Google Gemini API
- **Génération XML TEIF v1.8.8** conforme aux standards TTN
- **Aperçu en temps réel** du XML généré
- **Téléchargement direct** des fichiers XML
- **Validation complète** des fichiers uploadés
- **Sécurité renforcée** avec protection CSRF et validation

## 📁 Structure des Fichiers

```
/
├── index.php              # Page principale de l'interface
├── process.php            # Traitement des uploads et génération XML
├── script.js              # JavaScript pour l'interface utilisateur
├── config.php             # Configuration de l'application
├── test.php               # Page de test de la configuration
├── .htaccess              # Configuration Apache (sécurité)
├── ai_studio_code.py      # Script Python (requis)
├── FV_2025_0007.pdf       # Fichier de test (requis)
├── uploads/               # Répertoire des fichiers uploadés (créé automatiquement)
├── output/                # Répertoire des fichiers générés (créé automatiquement)
└── logs/                  # Répertoire des logs (créé automatiquement)
```

## 🚀 Installation

### Prérequis

1. **Serveur Web** (Apache/Nginx) avec PHP 7.4+
2. **Python 3.7+** avec les modules requis
3. **Extensions PHP** : fileinfo, json, curl
4. **Clé API Google Gemini** (optionnel pour l'extraction IA)

### Étapes d'installation

1. **Cloner/Copier les fichiers** dans votre répertoire web
2. **Configurer les permissions** :
   ```bash
   chmod 755 uploads/ output/ logs/
   chmod 644 *.php *.js
   ```
3. **Configurer la clé API** (optionnel) :
   ```bash
   export GEMINI_API_KEY="votre_cle_api"
   ```
4. **Tester l'installation** : Accéder à `test.php`

## 🔧 Configuration

### Variables d'environnement

```bash
# Clé API Gemini (optionnel)
export GEMINI_API_KEY="votre_cle_api_gemini"

# Configuration Python (si nécessaire)
export PYTHON_PATH="/usr/bin/python3"
```

### Configuration PHP

Modifiez `config.php` pour ajuster :
- Taille maximale des fichiers
- Répertoires de stockage
- Paramètres de sécurité
- Configuration de logging

### Configuration Apache

Le fichier `.htaccess` inclut :
- Headers de sécurité
- Protection des fichiers sensibles
- Compression et cache
- Limites d'upload

## 📖 Utilisation

### Interface Web

1. **Accéder à l'application** : Ouvrir `index.php` dans votre navigateur
2. **Télécharger un PDF** : Glisser-déposer ou cliquer pour sélectionner
3. **Choisir le mode** :
   - **Données de test** : Utilise des données prédéfinies (rapide)
   - **IA Gemini** : Extraction réelle du PDF (nécessite une clé API)
4. **Générer le XML** : Cliquer sur "Générer le XML TEIF"
5. **Télécharger le résultat** : Utiliser le bouton de téléchargement

### API REST (Programmation)

```javascript
// Exemple d'utilisation avec JavaScript
const formData = new FormData();
formData.append('pdf_file', fileInput.files[0]);
formData.append('extraction_mode', 'ai'); // ou 'mock'
formData.append('output_format', 'xml');

fetch('process.php', {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('XML généré:', data.xml_content);
    } else {
        console.error('Erreur:', data.error);
    }
});
```

## 🛡️ Sécurité

### Mesures implémentées

- **Validation des fichiers** : Type MIME, extension, taille
- **Protection CSRF** : Tokens de sécurité
- **Headers de sécurité** : XSS, clickjacking, MIME sniffing
- **Isolation des fichiers** : Répertoires protégés
- **Nettoyage automatique** : Suppression des fichiers temporaires
- **Logging sécurisé** : Traçabilité des actions

### Recommandations

1. **HTTPS obligatoire** en production
2. **Clés API sécurisées** via variables d'environnement
3. **Permissions restrictives** sur les répertoires
4. **Monitoring des logs** pour détecter les anomalies
5. **Mise à jour régulière** des dépendances

## 🔍 Tests et Débogage

### Page de test

Accéder à `test.php` pour vérifier :
- Configuration PHP
- Permissions des répertoires
- Disponibilité de Python
- Extensions PHP requises
- Configuration de l'IA

### Logs

Les logs sont stockés dans `logs/app.log` et incluent :
- Erreurs de traitement
- Informations de débogage
- Statistiques d'utilisation
- Tentatives de sécurité

### Débogage courant

1. **Erreur "Python non trouvé"** :
   - Vérifier l'installation de Python
   - Ajuster le chemin dans `config.php`

2. **Erreur "Permissions refusées"** :
   - Vérifier les permissions des répertoires
   - Configurer le propriétaire web (www-data)

3. **Erreur "Clé API invalide"** :
   - Vérifier la clé Gemini API
   - Utiliser le mode "mock" pour tester

## 📊 Monitoring

### Métriques disponibles

- Nombre de fichiers traités
- Temps de traitement moyen
- Taux de succès/échec
- Utilisation de l'IA vs données de test

### Alertes recommandées

- Erreurs répétées de traitement
- Tentatives d'upload de fichiers malveillants
- Utilisation excessive de l'API IA
- Espace disque insuffisant

## 🔄 Maintenance

### Tâches régulières

1. **Nettoyage des fichiers temporaires** :
   ```bash
   find uploads/ -type f -mtime +1 -delete
   find output/ -type f -mtime +7 -delete
   ```

2. **Rotation des logs** :
   ```bash
   logrotate /path/to/logrotate.conf
   ```

3. **Sauvegarde de la configuration** :
   ```bash
   tar -czf backup_config.tar.gz config.php .htaccess
   ```

## 🆘 Support

### Problèmes courants

- **Fichier trop volumineux** : Ajuster `upload_max_filesize` dans PHP
- **Timeout de traitement** : Augmenter `max_execution_time`
- **Mémoire insuffisante** : Augmenter `memory_limit`

### Ressources

- Documentation TEIF officielle
- API Google Gemini
- Bootstrap 5 documentation
- PHP documentation

## 📝 Licence

Ce code est fourni à des fins éducatives et de développement. Assurez-vous de respecter les conditions d'utilisation des APIs tierces utilisées.

## 🤝 Contribution

Pour contribuer au projet :
1. Fork le repository
2. Créer une branche feature
3. Commiter les changements
4. Créer une pull request

---

**Version** : 1.0.0  
**Dernière mise à jour** : 2025  
**Compatibilité** : PHP 7.4+, Python 3.7+, TEIF v1.8.8
