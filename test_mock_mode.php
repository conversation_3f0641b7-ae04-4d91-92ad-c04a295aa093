<?php
require_once 'config.php';

echo "<h2>Test du mode Mock</h2>";

// Simuler les données d'un fichier PDF uploadé
$testPdfPath = 'Exemple_facture_qr.pdf';
$extractionMode = 'mock';
$outputFormat = 'xml';
$manualSupplierData = [
    'Supplier Fiscal ID' => 'TEST123',
    'Supplier Name' => 'Test Supplier',
    'Supplier Address' => 'Test Address',
];

if (!file_exists($testPdfPath)) {
    echo "<div style='color: red;'>Fichier PDF de test non trouvé: $testPdfPath</div>";
    exit;
}

echo "Fichier PDF de test: <code>$testPdfPath</code><br>";
echo "Mode d'extraction: <code>$extractionMode</code><br>";

// Créer un script Python temporaire pour tester
$tempPythonScript = 'test_mock_' . uniqid() . '.py';
$manualSupplierJson = json_encode($manualSupplierData);

$pythonCode = '
import sys
import os
sys.path.append(".")

# Import our existing functions
from ai_studio_code import pdf_to_base64, extract_invoice_data_with_gemini, generate_teif_xml
import json

def main():
    pdf_path = "' . addslashes($testPdfPath) . '"
    extraction_mode = "' . $extractionMode . '"
    manual_supplier_data = json.loads(\'' . addslashes($manualSupplierJson) . '\')
    
    try:
        print("Starting PDF processing...")
        
        # Convert PDF to base64
        base64_data = pdf_to_base64(pdf_path)
        if not base64_data:
            raise Exception("Failed to convert PDF to base64")
        
        print("PDF converted to base64 successfully")
        
        # Extract invoice data (using mock mode)
        if extraction_mode == "ai":
            print("Using AI extraction...")
            invoice_data = extract_invoice_data_with_gemini(base64_data)
        else:
            print("Using mock data...")
            # Use mock data
            invoice_data = {
                "Invoice Number": "FV/2025/0007",
                "Issue Date": "06/02/2025",
                "Supplier Fiscal ID": "1327372W",
                "Supplier Name": "SOLAR ENERGY SOLUTIONS",
                "Supplier Address": "Zone Industrielle, Sfax, Tunisia",
                "Client Fiscal ID": "08777647",
                "Client Name": "EMNA KACEM",
                "Client Address": "RTE SOKRA KM 5, Sfax Sud, Tunisia",
                "Line Items": [
                    {
                        "ItemCode": "**********",
                        "Description": "MODULE PHOTOVOLTAIQUE JA SOLAR MONO 565 Wc",
                        "Quantity": 6.0,
                        "UnitPrice": 540.0,
                        "TaxRate": 7.0
                    },
                    {
                        "ItemCode": "**********", 
                        "Description": "ONDULEUR SOLIS MONO 3 KW",
                        "Quantity": 1.0,
                        "UnitPrice": 1870.0,
                        "TaxRate": 7.0
                    }
                ],
                "Total HT Amount": 4332.0,
                "Total TTC Amount": 4542.88,
                "Stamp Duty": 1.0,
                "Amount in Words": "Quatre Mille Cinq Cent Quarante-Deux Dinar et Huit Cent Quatre-Vingts Millimes"
            }
        
        if not invoice_data:
            raise Exception("Failed to extract invoice data")
        
        print("Invoice data extracted successfully")
        
        # Generate TEIF XML
        print("Generating TEIF XML...")
        xml_content = generate_teif_xml(invoice_data, manual_supplier_data)
        
        # Save XML to temporary file
        with open("output/temp_test_output.xml", "w", encoding="utf-8") as f:
            f.write(xml_content)
        
        # Save invoice data to JSON for PHP to read
        with open("output/temp_test_invoice_data.json", "w", encoding="utf-8") as f:
            json.dump(invoice_data, f, ensure_ascii=False, indent=2)
        
        print("SUCCESS: XML generated successfully")
        print("XML length:", len(xml_content))
        
    except Exception as e:
        print(f"ERROR: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
';

file_put_contents($tempPythonScript, $pythonCode);

echo "<h3>Exécution du script Python...</h3>";

$command = PYTHON_EXECUTABLE . " \"$tempPythonScript\" 2>&1";
echo "Commande: <code>$command</code><br><br>";

$output = [];
$returnCode = 0;
exec($command, $output, $returnCode);

echo "<strong>Code de retour:</strong> $returnCode<br>";
echo "<strong>Sortie:</strong><br>";
echo "<pre>" . implode("\n", $output) . "</pre>";

// Vérifier si les fichiers de sortie ont été créés
$xmlFile = 'output/temp_test_output.xml';
$jsonFile = 'output/temp_test_invoice_data.json';

echo "<h3>Vérification des fichiers de sortie</h3>";

if (file_exists($xmlFile)) {
    $xmlContent = file_get_contents($xmlFile);
    echo "<div style='color: green;'>✓ Fichier XML créé (" . strlen($xmlContent) . " caractères)</div>";
    echo "<details><summary>Contenu XML (cliquez pour voir)</summary>";
    echo "<pre>" . htmlspecialchars(substr($xmlContent, 0, 1000)) . "...</pre>";
    echo "</details>";
} else {
    echo "<div style='color: red;'>✗ Fichier XML non créé</div>";
}

if (file_exists($jsonFile)) {
    $jsonContent = file_get_contents($jsonFile);
    echo "<div style='color: green;'>✓ Fichier JSON créé (" . strlen($jsonContent) . " caractères)</div>";
} else {
    echo "<div style='color: red;'>✗ Fichier JSON non créé</div>";
}

// Nettoyage
unlink($tempPythonScript);
if (file_exists($xmlFile)) unlink($xmlFile);
if (file_exists($jsonFile)) unlink($jsonFile);

if ($returnCode === 0) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
    echo "<strong>✓ Le mode mock fonctionne correctement!</strong><br>";
    echo "L'application devrait maintenant générer des fichiers XML.";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin-top: 20px;'>";
    echo "<strong>✗ Le mode mock ne fonctionne pas.</strong><br>";
    echo "Il y a encore des problèmes à résoudre.";
    echo "</div>";
}
?>
